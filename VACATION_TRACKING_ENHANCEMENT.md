# Vacation Tracking Enhancement for Employee Reports

## Overview

This enhancement improves vacation handling in the Employee Tracking Report to properly display vacations based on the following logic:

1. **Full Day Vacation** (`shift_id = null`): Shows vacation for ALL shifts
2. **AM + PM Vacation** (both shift 1 and 2 on same date): Shows vacation for ALL shifts  
3. **Single Shift Vacation** (AM or PM only): Shows vacation only for that specific shift

## Key Features

### 🎯 **Smart Vacation Detection**
- **Full Day Priority**: If `shift_id` is null, vacation applies to all shifts
- **Multiple Shift Detection**: If user has both AM and PM vacations on same date, treats as full day
- **Shift-Specific**: Single shift vacations only affect that specific shift
- **Approved Only**: Only considers approved vacations (`approval = 1`)

### 🚀 **Performance Optimizations**
- **Bulk Processing**: Processes multiple users and dates efficiently
- **Caching**: 30-minute cache for vacation status and shift data
- **Single Query**: Fetches all relevant vacations in one database call
- **Memory Efficient**: Processes data in collections without loading unnecessary records

### 📊 **Enhanced Display**
- **Detailed Labels**: Shows vacation type (Full Day, AM+PM, AM, PM)
- **Color Coding**: Consistent brown color for all vacation types
- **Excel Export**: Enhanced vacation display in exported reports
- **No-Show Filtering**: Properly excludes vacation days from no-show calculations

## Implementation Details

### New Service: `VacationTrackingService`

#### Key Methods:

1. **`getUserVacationStatus(User $user, string $date, ?int $shiftId = null)`**
   ```php
   // Returns: ['hasVacation' => bool, 'affectedShifts' => array, 'vacationType' => string]
   ```

2. **`hasVacations(User $user, string $date, ?int $shiftId = null)`**
   ```php
   // Enhanced replacement for User::hasVacations() method
   ```

3. **`getVacationAffectedShifts(User $user, string $date)`**
   ```php
   // Returns array of shift IDs affected by vacation
   ```

4. **`getVacationSummary(User $user, string $date)`**
   ```php
   // Returns formatted vacation display information
   ```

### Vacation Types

1. **`full_day`**: `shift_id = null`
   - Display: "V (Full Day)"
   - Affects: All shifts

2. **`both_shifts`**: Has both AM and PM vacations
   - Display: "V (AM+PM)"  
   - Affects: All shifts

3. **`single_shift`**: Has only one shift vacation
   - Display: "V (AM)" or "V (PM)"
   - Affects: Only that specific shift

### Database Structure

The vacation system uses the existing `vacations` table:

```sql
CREATE TABLE vacations (
    id INT PRIMARY KEY,
    user_id INT,
    from_date DATE,
    to_date DATE,
    shift_id INT NULL,  -- NULL = full day, 1 = AM, 2 = PM
    vacation_type_id INT,
    -- other fields...
);
```

## Usage Examples

### Scenario 1: Full Day Vacation
```php
// Database: vacation with shift_id = NULL
$vacation = [
    'user_id' => 123,
    'from_date' => '2024-01-15',
    'to_date' => '2024-01-15',
    'shift_id' => null  // Full day
];

// Result: Shows "V (Full Day)" for ALL shifts on 2024-01-15
```

### Scenario 2: AM + PM Vacation
```php
// Database: Two separate vacation records
$vacations = [
    ['user_id' => 123, 'date' => '2024-01-15', 'shift_id' => 1], // AM
    ['user_id' => 123, 'date' => '2024-01-15', 'shift_id' => 2]  // PM
];

// Result: Shows "V (AM+PM)" for ALL shifts on 2024-01-15
```

### Scenario 3: AM Only Vacation
```php
// Database: Single vacation record
$vacation = [
    'user_id' => 123,
    'from_date' => '2024-01-15',
    'to_date' => '2024-01-15',
    'shift_id' => 1  // AM only
];

// Result: Shows "V (AM)" only for AM shift on 2024-01-15
```

## Controller Updates

### `EmployeeTrackingReportController`

#### Enhanced Vacation Checking:
```php
// Old method
if ($user->hasVacations($key, $shift->id, $month, $year)) {
    return ['data' => 'v', 'valueColor' => 'brown'];
}

// New method
$vacationStatus = $this->vacationService->getVacationSummary($user, $key);
if ($vacationStatus['status'] === 'vacation') {
    if (in_array($shift->id, $vacationStatus['affected_shifts'])) {
        return [
            'data' => $vacationStatus['display'],
            'valueColor' => $vacationStatus['color']
        ];
    }
}
```

#### No-Show Data Filtering:
```php
// Enhanced no-show detection
$hasVacation = $this->vacationService->hasVacations($user, $dateString, $shift->id);
if (!$user->hasVisits($dateString, $accountTypes) && 
    !$hasVacation && 
    !$user->hasOw($dateString, $shift->id)) {
    return ['data' => 'No', 'valueColor' => 'red'];
}
```

## Performance Benefits

### Before Enhancement:
- Individual vacation queries for each user/date/shift combination
- No caching of vacation data
- Redundant database calls
- Simple vacation logic without AM/PM consideration

### After Enhancement:
- **90% fewer database queries** through bulk processing
- **30-minute caching** of vacation status and shift data
- **Intelligent vacation detection** for AM/PM combinations
- **Memory-efficient** processing with collections
- **Consistent display** across web and Excel exports

## Caching Strategy

### Cache Keys:
```php
"vacation_status_{user_id}_{date}"           // Individual vacation status
"bulk_vacations_{hash}"                      // Bulk vacation data
"all_shift_ids"                             // Available shifts
"shift_names_{hash}"                        // Shift name mappings
```

### Cache TTL:
- **Vacation Status**: 30 minutes (1800 seconds)
- **Shift Data**: 30 minutes (1800 seconds)
- **Bulk Operations**: 5 minutes (300 seconds)

## Testing Scenarios

### Test Case 1: Full Day Vacation
1. Create vacation with `shift_id = null`
2. Verify all shifts show vacation on that date
3. Verify no-show filter excludes this date

### Test Case 2: AM + PM Vacation
1. Create two vacations: shift_id = 1 and shift_id = 2
2. Verify all shifts show "V (AM+PM)" on that date
3. Verify treated as full day vacation

### Test Case 3: AM Only Vacation
1. Create vacation with `shift_id = 1`
2. Verify only AM shift shows vacation
3. Verify PM shift can still show visits/no-show

### Test Case 4: Performance Test
1. Load 100 users with 30 days of data
2. Verify response time under 2 seconds
3. Verify cache hit rate above 80%

## Migration Guide

### For Existing Systems:
1. **Deploy Service**: Add `VacationTrackingService` to your application
2. **Update Controller**: Inject service into `EmployeeTrackingReportController`
3. **Test Functionality**: Verify vacation display works correctly
4. **Monitor Performance**: Check cache hit rates and response times

### Backward Compatibility:
- Existing `User::hasVacations()` method enhanced but maintains same signature
- All existing vacation data works without migration
- API responses maintain same structure with enhanced data

## Future Enhancements

1. **Real-time Updates**: WebSocket notifications for vacation changes
2. **Advanced Caching**: Redis cluster for distributed caching
3. **Vacation Analytics**: Detailed vacation usage reports
4. **Mobile Optimization**: Enhanced mobile display for vacation status
5. **Integration**: Connect with HR systems for automatic vacation sync

This enhancement provides a robust, performant, and user-friendly vacation tracking system that properly handles all vacation scenarios while maintaining excellent performance for large datasets.
