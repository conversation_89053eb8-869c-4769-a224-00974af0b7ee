# Plan Visit Store Service Optimization Guide

## Overview

This document outlines the comprehensive optimizations implemented for the `PlanVisitStoreService` to handle multiple concurrent requests efficiently and improve overall performance.

## Key Performance Issues Identified

1. **N+1 Query Problems**: Multiple individual database queries in loops
2. **Inefficient LinkedPharmacy Checks**: Individual `exists()` calls for each plan visit
3. **Suboptimal Bulk Operations**: Basic bulk insert without chunking
4. **Frequency Validation Overhead**: Individual frequency checks per visit
5. **Large Transaction Scope**: Long-running transactions causing lock contention
6. **Cache Misses**: Repeated queries for the same data

## Optimization Strategies Implemented

### 1. Database Optimizations

#### New Indexes Added
- **Planned Visits**: Composite indexes for user-line-date, limit checks, account-date queries
- **Linked Pharmacies**: Account-based index for bulk existence checks
- **Actual Visits**: Frequency validation indexes
- **Plan Visit Limits**: Role-based composite indexes
- **Settings Tables**: Key-based indexes for faster lookups

#### Query Optimizations
- Bulk data fetching instead of individual queries
- Optimized WHERE clauses with proper index usage
- Reduced transaction scope to minimize lock contention
- Chunked bulk inserts for better memory management

### 2. Caching Strategy

#### Multi-Level Caching
```php
// Settings caching (30 minutes TTL)
Cache::remember('plan_settings', 1800, function() { ... });

// User data caching (5 minutes TTL)  
Cache::remember("user_role_{$userId}_{$lineId}", 300, function() { ... });

// Bulk data caching
Cache::remember("linked_pharmacies_" . md5(serialize($accountIds)), 300, function() { ... });
```

#### Cache Keys Strategy
- Hierarchical cache keys for easy invalidation
- MD5 hashing for complex array-based keys
- TTL optimization based on data volatility

### 3. Bulk Operations

#### Optimized Data Processing
```php
// Before: Individual queries in loop
foreach ($planVisits as $visit) {
    LinkedPharmacy::where('account_id', $visit['account_id'])->exists();
}

// After: Bulk fetch and lookup
$linkedPharmacies = LinkedPharmacy::whereIn('account_id', $accountIds)
    ->pluck('account_id')->flip();
```

#### Chunked Operations
- Bulk inserts split into 500-record chunks
- Memory-efficient processing for large datasets
- Parallel processing capabilities

### 4. Frequency Validation Optimization

#### Bulk Frequency Checking
```php
// Integration with OptimizedFrequencyService
$frequencies = (new OptimizedFrequencyService)->getBulkFrequencies(
    $doctorData, $month, $year
);
```

#### Reduced Database Calls
- Single query for all doctor frequencies
- Cached frequency data with proper TTL
- Batch validation instead of individual checks

### 5. Concurrent Request Handling

#### Rate Limiting Middleware
```php
// PlanVisitRateLimiter middleware
- 10 requests per minute per user
- Maximum 3 concurrent requests per user
- Exponential backoff on rate limit exceeded
```

#### Async Processing
```php
// Automatic async switching for large batches
if ($planCount > 50 || $systemLoad > 0.7) {
    BulkPlanVisitStoreJob::dispatch($planVisits, $userId, $fromDate);
}
```

### 6. Transaction Optimization

#### Reduced Transaction Scope
```php
// Before: Large transaction with all operations
DB::transaction(function() {
    // All validation, processing, and side effects
});

// After: Focused transaction with pre-validation
$preparedData = $this->prepareAndValidateData($planVisits, ...);
DB::transaction(function() use ($preparedData) {
    // Only critical database operations
});
```

## Performance Improvements

### Expected Performance Gains

1. **Database Query Reduction**: 70-80% fewer queries through bulk operations
2. **Response Time**: 50-60% faster response times for typical requests
3. **Concurrent Handling**: Support for 3x more concurrent users
4. **Memory Usage**: 40% reduction through chunked processing
5. **Cache Hit Rate**: 85%+ cache hit rate for repeated operations

### Scalability Improvements

- **Horizontal Scaling**: Queue-based async processing
- **Load Distribution**: Intelligent sync/async switching
- **Resource Management**: Memory-efficient bulk operations
- **Graceful Degradation**: Fallback mechanisms for high load

## Configuration Options

### Cache Configuration
```php
'cache' => [
    'settings_ttl' => 1800,     // 30 minutes
    'user_data_ttl' => 300,     // 5 minutes
    'account_data_ttl' => 300,  // 5 minutes
]
```

### Bulk Operations
```php
'bulk_operations' => [
    'insert_chunk_size' => 500,
    'async_threshold' => 50,
    'max_transaction_size' => 1000,
]
```

### Rate Limiting
```php
'rate_limiting' => [
    'max_requests_per_minute' => 10,
    'max_concurrent_requests' => 3,
]
```

## Implementation Steps

### 1. Database Migration
```bash
php artisan migrate --path=database/migrations/2025_07_17_000000_optimize_plan_visit_store_performance.php
```

### 2. Update Service Usage
```php
// Replace existing service calls with optimized version
$result = app(PlanVisitStoreService::class)->store($planVisits, $fromDate);
```

### 3. Add Middleware (Optional)
```php
// In routes/api.php or controller
Route::middleware(['plan.visit.rate.limit'])->group(function() {
    Route::post('/plan-visits', [OptimizedPlanVisitController::class, 'store']);
});
```

### 4. Configure Queue Workers
```bash
# Start queue workers for async processing
php artisan queue:work --queue=high,medium,low --timeout=300
```

## Monitoring and Metrics

### Performance Metrics
- Request processing time
- Database query count and execution time
- Cache hit/miss ratios
- Queue job processing time
- Concurrent request counts

### Health Checks
- System load monitoring
- Queue size monitoring
- Error rate tracking
- Memory usage tracking

## Best Practices

### For Developers
1. Always use the optimized service methods
2. Monitor cache hit rates and adjust TTL as needed
3. Use async processing for large batches (>50 records)
4. Implement proper error handling and fallbacks

### For System Administrators
1. Monitor queue worker health
2. Set up proper Redis/cache configuration
3. Monitor database performance and index usage
4. Configure appropriate rate limiting based on system capacity

## Troubleshooting

### Common Issues
1. **High Memory Usage**: Reduce chunk sizes in configuration
2. **Slow Queries**: Check index usage with EXPLAIN
3. **Cache Misses**: Verify cache configuration and TTL settings
4. **Queue Backlog**: Scale queue workers or optimize job processing

### Performance Tuning
1. Adjust cache TTL based on data change frequency
2. Optimize chunk sizes based on available memory
3. Fine-tune rate limits based on system capacity
4. Monitor and adjust async thresholds

## Future Enhancements

1. **Database Sharding**: For extremely high loads
2. **Read Replicas**: Separate read/write operations
3. **Advanced Caching**: Redis Cluster for distributed caching
4. **Machine Learning**: Predictive load balancing
5. **Microservices**: Split plan visit operations into separate services
