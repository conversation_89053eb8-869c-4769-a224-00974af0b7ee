<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Plan Visit Store Optimization Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for optimizing plan visit
    | store operations, especially for handling concurrent requests.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Caching Configuration
    |--------------------------------------------------------------------------
    */
    'cache' => [
        // Cache TTL for various data types (in seconds)
        'settings_ttl' => 1800, // 30 minutes
        'user_data_ttl' => 300,  // 5 minutes
        'account_data_ttl' => 300, // 5 minutes
        'frequency_data_ttl' => 600, // 10 minutes
        'plan_limits_ttl' => 1800, // 30 minutes
        
        // Cache key prefixes
        'prefixes' => [
            'settings' => 'plan_settings',
            'user_role' => 'user_role',
            'account_shifts' => 'account_shifts',
            'linked_pharmacies' => 'linked_pharmacies',
            'plan_limits' => 'plan_limits',
            'frequencies' => 'frequencies'
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Bulk Operations Configuration
    |--------------------------------------------------------------------------
    */
    'bulk_operations' => [
        // Chunk size for bulk inserts
        'insert_chunk_size' => 500,
        
        // Maximum records to process in a single transaction
        'max_transaction_size' => 1000,
        
        // Threshold for switching to async processing
        'async_threshold' => 50,
        
        // Batch size for frequency validation
        'frequency_batch_size' => 100
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        // Maximum requests per minute per user
        'max_requests_per_minute' => 10,
        
        // Maximum concurrent requests per user
        'max_concurrent_requests' => 3,
        
        // Rate limit decay time in minutes
        'decay_minutes' => 1,
        
        // Concurrent request timeout in seconds
        'concurrent_timeout' => 300
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        // Enable performance logging
        'enable_logging' => env('PLAN_VISIT_PERFORMANCE_LOGGING', true),
        
        // Log slow operations (in milliseconds)
        'slow_operation_threshold' => 1000,
        
        // Enable metrics collection
        'enable_metrics' => env('PLAN_VISIT_METRICS', true),
        
        // Metrics retention period (in hours)
        'metrics_retention' => 24
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    */
    'queue' => [
        // Queue name for bulk operations
        'bulk_operations_queue' => 'high',
        
        // Queue name for notifications
        'notifications_queue' => 'medium',
        
        // Job timeout in seconds
        'job_timeout' => 300,
        
        // Number of job retries
        'job_retries' => 3,
        
        // Backoff strategy (in seconds)
        'job_backoff' => [10, 30, 60]
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Optimization
    |--------------------------------------------------------------------------
    */
    'database' => [
        // Enable query optimization
        'enable_query_optimization' => true,
        
        // Use read replicas for heavy queries
        'use_read_replicas' => env('DB_READ_REPLICA_ENABLED', false),
        
        // Connection timeout for bulk operations
        'bulk_connection_timeout' => 60,
        
        // Enable database query logging for optimization
        'log_queries' => env('LOG_DATABASE_QUERIES', false)
    ],

    /*
    |--------------------------------------------------------------------------
    | System Load Thresholds
    |--------------------------------------------------------------------------
    */
    'load_thresholds' => [
        // System load threshold for switching to async (0.0 to 1.0)
        'async_switch_threshold' => 0.7,
        
        // High load threshold for additional optimizations
        'high_load_threshold' => 0.8,
        
        // Critical load threshold for request rejection
        'critical_load_threshold' => 0.9,
        
        // Load calculation factors
        'load_factors' => [
            'concurrent_users_weight' => 0.4,
            'cache_hit_rate_weight' => 0.3,
            'queue_size_weight' => 0.3
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    */
    'error_handling' => [
        // Enable graceful degradation
        'enable_graceful_degradation' => true,
        
        // Fallback to basic operations on errors
        'fallback_on_errors' => true,
        
        // Maximum error retry attempts
        'max_retry_attempts' => 3,
        
        // Error notification settings
        'notify_on_errors' => env('NOTIFY_PLAN_VISIT_ERRORS', false),
        'error_notification_threshold' => 5 // errors per minute
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        // Enable bulk frequency validation
        'bulk_frequency_validation' => true,
        
        // Enable optimized linked pharmacy checks
        'optimized_pharmacy_checks' => true,
        
        // Enable plan limitation caching
        'cached_plan_limitations' => true,
        
        // Enable concurrent request detection
        'concurrent_request_detection' => true,
        
        // Enable automatic async switching
        'auto_async_switching' => true
    ]
];
