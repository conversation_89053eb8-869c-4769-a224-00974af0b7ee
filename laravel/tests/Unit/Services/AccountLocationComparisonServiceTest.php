<?php

namespace Tests\Unit\Services;

use App\Services\AccountLocationComparisonService;
use App\Services\DifferenceBetweenTwoCoordinates;
use Mockery;
use Tests\TestCase;

/**
 * Test class for AccountLocationComparisonService
 *
 * Tests the service's ability to validate account_type_ids parameters.
 *
 * @covers \App\Services\AccountLocationComparisonService
 */
class AccountLocationComparisonServiceTest extends TestCase
{
    private $mockDistanceCalculator;
    private AccountLocationComparisonService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockDistanceCalculator = Mockery::mock(DifferenceBetweenTwoCoordinates::class);
        $this->service = new AccountLocationComparisonService($this->mockDistanceCalculator);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test validation structure for account_type_ids parameter
     */
    public function test_validates_account_type_ids_structure(): void
    {
        // Test that the validation method handles account_type_ids parameter
        // without actually hitting the database

        // Act - Test with empty account_type_ids (should be valid)
        $result = $this->service->validateReportParameters([
            'account_type_ids' => []
        ]);

        // Assert - Empty array should be valid
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
    }

    /**
     * Test validation fails when account_type_ids is not an array
     */
    public function test_validation_fails_when_account_type_ids_not_array(): void
    {
        // Act
        $result = $this->service->validateReportParameters([
            'account_type_ids' => 'not-an-array'
        ]);

        // Assert
        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('account_type_ids', $result['errors']);
        $this->assertEquals('Account Type IDs must be provided as an array', $result['errors']['account_type_ids']);
    }

    /**
     * Test validation fails for invalid account type IDs
     */
    public function test_validation_fails_for_invalid_account_type_ids(): void
    {
        // Act
        $result = $this->service->validateReportParameters([
            'account_type_ids' => ['invalid', -1, 0]
        ]);

        // Assert
        $this->assertFalse($result['valid']);
        $this->assertArrayHasKey('account_type_ids', $result['errors']);
        $this->assertStringContainsString('invalid', $result['errors']['account_type_ids']);
        $this->assertStringContainsString('-1', $result['errors']['account_type_ids']);
        $this->assertStringContainsString('0', $result['errors']['account_type_ids']);
    }

    /**
     * Test that service handles account_type_ids parameter structure
     */
    public function test_service_handles_account_type_ids_parameter(): void
    {
        // Test that the service can process account_type_ids without errors
        // This is a structural test to ensure the parameter is handled

        // Use reflection to test the private method signature
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('getAccountsForAnalysis');
        $method->setAccessible(true);

        // Check that the method accepts the account_type_ids parameter
        $parameters = $method->getParameters();
        $this->assertCount(3, $parameters);
        $this->assertEquals('accountTypeIds', $parameters[2]->getName());
        $this->assertTrue($parameters[2]->allowsNull());
    }
}
