<?php

namespace Tests\Unit;

use App\Http\Requests\AccountBulkUpdateLocationsRequest;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\Validator;

class AccountBulkUpdateLocationsTest extends TestCase
{
    use WithFaker;

    /**
     * Test request validation with valid data
     *
     * @return void
     */
    public function test_request_validation_with_valid_data()
    {
        $data = [
            'updates' => [
                [
                    'account_id' => 123,
                    'account_name' => 'Test Account',
                    'current_latitude' => 30.0444,
                    'current_longitude' => 31.2357,
                    'new_latitude' => 30.0445,
                    'new_longitude' => 31.2358,
                    'confidence_score' => 85,
                    'visit_frequency' => 12,
                    'visits_count' => 8,
                    'average_distance_improvement' => 45.5,
                    'reason' => 'High visit frequency (12 visits), Low average distance (25m)'
                ]
            ],
            'analysis_metadata' => [
                'distance_threshold' => 100,
                'analysis_date' => '2025-01-17 10:30:00',
                'total_accounts_analyzed' => 5,
                'total_visits_analyzed' => 45
            ]
        ];

        $request = new AccountBulkUpdateLocationsRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertFalse($validator->fails());
    }

    /**
     * Test request validation with missing required fields
     *
     * @return void
     */
    public function test_request_validation_with_missing_required_fields()
    {
        $data = [
            'updates' => [
                [
                    'account_id' => 123,
                    // Missing new_latitude and new_longitude
                ]
            ]
        ];

        $request = new AccountBulkUpdateLocationsRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('updates.0.new_latitude', $validator->errors()->toArray());
        $this->assertArrayHasKey('updates.0.new_longitude', $validator->errors()->toArray());
    }

    /**
     * Test request validation with invalid data types
     *
     * @return void
     */
    public function test_request_validation_with_invalid_data_types()
    {
        $data = [
            'updates' => [
                [
                    'account_id' => 'invalid',
                    'new_latitude' => 'not_a_number',
                    'new_longitude' => 'not_a_number',
                    'confidence_score' => 150, // Over 100
                ]
            ]
        ];

        $request = new AccountBulkUpdateLocationsRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('updates.0.account_id', $validator->errors()->toArray());
        $this->assertArrayHasKey('updates.0.new_latitude', $validator->errors()->toArray());
        $this->assertArrayHasKey('updates.0.new_longitude', $validator->errors()->toArray());
        $this->assertArrayHasKey('updates.0.confidence_score', $validator->errors()->toArray());
    }

    /**
     * Test request validation with empty updates array
     *
     * @return void
     */
    public function test_request_validation_with_empty_updates()
    {
        $data = [
            'updates' => []
        ];

        $request = new AccountBulkUpdateLocationsRequest();
        $validator = Validator::make($data, $request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('updates', $validator->errors()->toArray());
    }
}
