<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\AccountLocationComparisonController;
use App\Services\AccountLocationComparisonService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

use Mockery;
use Tests\TestCase;

/**
 * Test class for AccountLocationComparisonController
 *
 * Tests the controller's ability to handle AccountType filtering
 * and validate request parameters properly.
 *
 * @covers \App\Http\Controllers\AccountLocationComparisonController
 */
class AccountLocationComparisonControllerTest extends TestCase
{
    private $mockService;
    private AccountLocationComparisonController $controller;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockService = Mockery::mock(AccountLocationComparisonService::class);
        $this->controller = new AccountLocationComparisonController($this->mockService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that account_type_ids parameter is properly validated
     */
    public function test_validates_account_type_ids_parameter(): void
    {
        // Arrange
        $request = Request::create('/api/account-location-comparison', 'POST', [
            'account_type_ids' => [1, 2, 3],
            'line_ids' => [1, 2],
            'div_ids' => [10, 20],
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31',
            'distance_threshold' => 1000,
            'recent_visits_limit' => 5
        ]);

        // Mock service validation
        $this->mockService->shouldReceive('validateReportParameters')
            ->once()
            ->with(Mockery::on(function ($params) {
                return isset($params['account_type_ids']) && 
                       $params['account_type_ids'] === [1, 2, 3];
            }))
            ->andReturn(['valid' => true, 'errors' => []]);

        // Mock service report generation
        $this->mockService->shouldReceive('generateLocationComparisonReport')
            ->once()
            ->andReturn([
                'success' => true,
                'summary' => [
                    'total_accounts_analyzed' => 5,
                    'accounts_with_discrepancies' => 2
                ],
                'accounts' => []
            ]);

        // Act
        $response = $this->controller->generateReport($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * Test that controller validation rules include account_type_ids
     */
    public function test_controller_validation_rules_include_account_type_ids(): void
    {
        // This test verifies that the validation rules are properly set up
        // by checking the controller's validateRequest method indirectly

        // Arrange - Create a reflection to access private method
        $reflection = new \ReflectionClass($this->controller);
        $method = $reflection->getMethod('validateRequest');
        $method->setAccessible(true);

        // Create a request with valid account_type_ids
        $request = Request::create('/api/test', 'POST', [
            'account_type_ids' => [1, 2, 3],
            'distance_threshold' => 1000
        ]);

        // Act - Call the private validation method
        $result = $method->invoke($this->controller, $request);

        // Assert - The validation should pass and include account_type_ids
        $this->assertIsArray($result);
        $this->assertArrayHasKey('account_type_ids', $result);
        $this->assertEquals([1, 2, 3], $result['account_type_ids']);
    }

    /**
     * Test that getReportInfo includes account_type_ids parameter documentation
     */
    public function test_get_report_info_includes_account_type_ids(): void
    {
        // Act
        $response = $this->controller->getReportInfo();

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $data = $response->getData(true);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('parameters', $data['data']);
        $this->assertArrayHasKey('account_type_ids', $data['data']['parameters']);
        
        $accountTypeParam = $data['data']['parameters']['account_type_ids'];
        $this->assertEquals('array', $accountTypeParam['type']);
        $this->assertFalse($accountTypeParam['required']);
        $this->assertStringContainsString('Account Type IDs', $accountTypeParam['description']);
    }

    /**
     * Test successful report generation with account_type_ids filter
     */
    public function test_successful_report_generation_with_account_type_filter(): void
    {
        // Arrange
        $request = Request::create('/api/account-location-comparison', 'POST', [
            'account_type_ids' => [1, 2],
            'distance_threshold' => 500
        ]);

        $expectedReport = [
            'success' => true,
            'summary' => [
                'total_accounts_analyzed' => 3,
                'accounts_with_discrepancies' => 1,
                'total_visits_analyzed' => 15,
                'average_distance_variance' => 250.5,
                'max_distance_variance' => 800.0,
                'accounts_without_registered_location' => 0,
                'accounts_without_visits' => 0
            ],
            'accounts' => [
                [
                    'account_id' => 1,
                    'account_name' => 'Test Account',
                    'account_code' => 'ACC001',
                    'has_discrepancies' => true
                ]
            ]
        ];

        // Mock service validation
        $this->mockService->shouldReceive('validateReportParameters')
            ->once()
            ->andReturn(['valid' => true, 'errors' => []]);

        // Mock service report generation
        $this->mockService->shouldReceive('generateLocationComparisonReport')
            ->once()
            ->with(Mockery::on(function ($params) {
                return isset($params['account_type_ids']) && 
                       $params['account_type_ids'] === [1, 2] &&
                       $params['distance_threshold'] === 500;
            }))
            ->andReturn($expectedReport);

        // Act
        $response = $this->controller->generateReport($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedReport, $responseData['data']);
    }

    /**
     * Test that location_filter parameter is properly validated
     */
    public function test_validates_location_filter_parameter(): void
    {
        // Arrange
        $request = Request::create('/api/account-location-comparison', 'POST', [
            'location_filter' => 'with_location',
            'line_ids' => [1, 2],
            'distance_threshold' => 1000
        ]);

        // Mock service validation
        $this->mockService->shouldReceive('validateReportParameters')
            ->once()
            ->with(Mockery::on(function ($params) {
                return isset($params['location_filter']) &&
                       $params['location_filter'] === 'with_location';
            }))
            ->andReturn(['valid' => true, 'errors' => []]);

        // Mock service report generation
        $this->mockService->shouldReceive('generateLocationComparisonReport')
            ->once()
            ->andReturn([
                'success' => true,
                'summary' => [
                    'total_accounts_analyzed' => 3,
                    'accounts_with_discrepancies' => 1
                ],
                'accounts' => []
            ]);

        // Act
        $response = $this->controller->generateReport($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * Test location_filter validation with invalid value
     */
    public function test_location_filter_validation_with_invalid_value(): void
    {
        // Arrange
        $request = Request::create('/api/account-location-comparison', 'POST', [
            'location_filter' => 'invalid_value'
        ]);

        // Act & Assert
        $this->expectException(\Illuminate\Validation\ValidationException::class);
        $this->controller->generateReport($request);
    }

    /**
     * Test that getReportInfo includes location_filter parameter documentation
     */
    public function test_get_report_info_includes_location_filter(): void
    {
        // Act
        $response = $this->controller->getReportInfo();

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $data = $response->getData(true);
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey('parameters', $data['data']);
        $this->assertArrayHasKey('location_filter', $data['data']['parameters']);

        $locationFilterParam = $data['data']['parameters']['location_filter'];
        $this->assertEquals('string', $locationFilterParam['type']);
        $this->assertFalse($locationFilterParam['required']);
        $this->assertArrayHasKey('enum', $locationFilterParam);
        $this->assertEquals(['with_location', 'without_location'], $locationFilterParam['enum']);
        $this->assertStringContainsString('Filter accounts by location presence', $locationFilterParam['description']);
    }

    /**
     * Test successful report generation with location_filter for accounts with location
     */
    public function test_successful_report_generation_with_location_filter_with_location(): void
    {
        // Arrange
        $request = Request::create('/api/account-location-comparison', 'POST', [
            'location_filter' => 'with_location',
            'distance_threshold' => 500
        ]);

        $expectedReport = [
            'success' => true,
            'summary' => [
                'total_accounts_analyzed' => 5,
                'accounts_with_discrepancies' => 2,
                'total_visits_analyzed' => 25,
                'average_distance_variance' => 150.0,
                'max_distance_variance' => 600.0,
                'accounts_without_registered_location' => 0,
                'accounts_without_visits' => 1
            ],
            'accounts' => [
                [
                    'account_id' => 1,
                    'account_name' => 'Test Account With Location',
                    'account_code' => 'ACC001',
                    'has_discrepancies' => true,
                    'registered_location' => [
                        'has_location' => true,
                        'latitude' => 30.0444,
                        'longitude' => 31.2357
                    ]
                ]
            ]
        ];

        // Mock service validation
        $this->mockService->shouldReceive('validateReportParameters')
            ->once()
            ->andReturn(['valid' => true, 'errors' => []]);

        // Mock service report generation
        $this->mockService->shouldReceive('generateLocationComparisonReport')
            ->once()
            ->with(Mockery::on(function ($params) {
                return isset($params['location_filter']) &&
                       $params['location_filter'] === 'with_location' &&
                       $params['distance_threshold'] === 500;
            }))
            ->andReturn($expectedReport);

        // Act
        $response = $this->controller->generateReport($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedReport, $responseData['data']);
    }

    /**
     * Test successful report generation with location_filter for accounts without location
     */
    public function test_successful_report_generation_with_location_filter_without_location(): void
    {
        // Arrange
        $request = Request::create('/api/account-location-comparison', 'POST', [
            'location_filter' => 'without_location',
            'line_ids' => [1, 2]
        ]);

        $expectedReport = [
            'success' => true,
            'summary' => [
                'total_accounts_analyzed' => 3,
                'accounts_with_discrepancies' => 0,
                'total_visits_analyzed' => 0,
                'average_distance_variance' => 0,
                'max_distance_variance' => 0,
                'accounts_without_registered_location' => 3,
                'accounts_without_visits' => 3
            ],
            'accounts' => [
                [
                    'account_id' => 2,
                    'account_name' => 'Test Account Without Location',
                    'account_code' => 'ACC002',
                    'has_discrepancies' => false,
                    'registered_location' => [
                        'has_location' => false
                    ]
                ]
            ]
        ];

        // Mock service validation
        $this->mockService->shouldReceive('validateReportParameters')
            ->once()
            ->andReturn(['valid' => true, 'errors' => []]);

        // Mock service report generation
        $this->mockService->shouldReceive('generateLocationComparisonReport')
            ->once()
            ->with(Mockery::on(function ($params) {
                return isset($params['location_filter']) &&
                       $params['location_filter'] === 'without_location' &&
                       isset($params['line_ids']) &&
                       $params['line_ids'] === [1, 2];
            }))
            ->andReturn($expectedReport);

        // Act
        $response = $this->controller->generateReport($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($expectedReport, $responseData['data']);
    }
}
