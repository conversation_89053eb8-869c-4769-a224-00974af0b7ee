<?php

namespace Tests\Feature;

use App\Services\VacationTrackingService;
use App\User;
use App\Vacation;
use App\VacationType;
use App\Shift;
use App\PlanVisitDetails;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VacationTrackingTest extends TestCase
{
    use RefreshDatabase;

    private VacationTrackingService $vacationService;
    private User $user;
    private VacationType $vacationType;
    private Shift $amShift;
    private Shift $pmShift;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->vacationService = new VacationTrackingService();
        
        // Create test data
        $this->user = User::factory()->create();
        $this->vacationType = VacationType::factory()->create();
        $this->amShift = Shift::factory()->create(['id' => 1, 'name' => 'AM']);
        $this->pmShift = Shift::factory()->create(['id' => 2, 'name' => 'PM']);
    }

    /** @test */
    public function it_detects_full_day_vacation()
    {
        // Create full day vacation (shift_id = null)
        $vacation = $this->createVacation('2024-01-15', '2024-01-15', null);
        $this->approveVacation($vacation);

        // Test AM shift
        $status = $this->vacationService->getUserVacationStatus($this->user, '2024-01-15', 1);
        
        $this->assertTrue($status['hasVacation']);
        $this->assertEquals('full_day', $status['vacationType']);
        $this->assertContains(1, $status['affectedShifts']);
        $this->assertContains(2, $status['affectedShifts']);

        // Test PM shift
        $this->assertTrue($this->vacationService->hasVacations($this->user, '2024-01-15', 2));
    }

    /** @test */
    public function it_detects_am_plus_pm_vacation_as_full_day()
    {
        // Create AM vacation
        $amVacation = $this->createVacation('2024-01-15', '2024-01-15', 1);
        $this->approveVacation($amVacation);

        // Create PM vacation
        $pmVacation = $this->createVacation('2024-01-15', '2024-01-15', 2);
        $this->approveVacation($pmVacation);

        $status = $this->vacationService->getUserVacationStatus($this->user, '2024-01-15');
        
        $this->assertTrue($status['hasVacation']);
        $this->assertEquals('both_shifts', $status['vacationType']);
        $this->assertContains(1, $status['affectedShifts']);
        $this->assertContains(2, $status['affectedShifts']);

        // Both shifts should show vacation
        $this->assertTrue($this->vacationService->hasVacations($this->user, '2024-01-15', 1));
        $this->assertTrue($this->vacationService->hasVacations($this->user, '2024-01-15', 2));
    }

    /** @test */
    public function it_detects_single_shift_vacation()
    {
        // Create AM only vacation
        $vacation = $this->createVacation('2024-01-15', '2024-01-15', 1);
        $this->approveVacation($vacation);

        $status = $this->vacationService->getUserVacationStatus($this->user, '2024-01-15');
        
        $this->assertTrue($status['hasVacation']);
        $this->assertEquals('single_shift', $status['vacationType']);
        $this->assertContains(1, $status['affectedShifts']);
        $this->assertNotContains(2, $status['affectedShifts']);

        // Only AM shift should show vacation
        $this->assertTrue($this->vacationService->hasVacations($this->user, '2024-01-15', 1));
        $this->assertFalse($this->vacationService->hasVacations($this->user, '2024-01-15', 2));
    }

    /** @test */
    public function it_ignores_unapproved_vacations()
    {
        // Create vacation but don't approve it
        $vacation = $this->createVacation('2024-01-15', '2024-01-15', null);
        // Don't approve the vacation

        $status = $this->vacationService->getUserVacationStatus($this->user, '2024-01-15');
        
        $this->assertFalse($status['hasVacation']);
        $this->assertEquals('none', $status['vacationType']);
        $this->assertEmpty($status['affectedShifts']);
    }

    /** @test */
    public function it_handles_multi_day_vacations()
    {
        // Create 3-day vacation
        $vacation = $this->createVacation('2024-01-15', '2024-01-17', null);
        $this->approveVacation($vacation);

        // Test each day
        for ($day = 15; $day <= 17; $day++) {
            $date = "2024-01-{$day}";
            $status = $this->vacationService->getUserVacationStatus($this->user, $date);
            
            $this->assertTrue($status['hasVacation'], "Day {$day} should have vacation");
            $this->assertEquals('full_day', $status['vacationType']);
        }

        // Test day before and after
        $this->assertFalse($this->vacationService->hasVacations($this->user, '2024-01-14'));
        $this->assertFalse($this->vacationService->hasVacations($this->user, '2024-01-18'));
    }

    /** @test */
    public function it_provides_vacation_summary()
    {
        // Test full day vacation
        $vacation = $this->createVacation('2024-01-15', '2024-01-15', null);
        $this->approveVacation($vacation);

        $summary = $this->vacationService->getVacationSummary($this->user, '2024-01-15');
        
        $this->assertEquals('vacation', $summary['status']);
        $this->assertEquals('V (Full Day)', $summary['display']);
        $this->assertEquals('brown', $summary['color']);
        $this->assertEquals('full_day', $summary['type']);

        // Test AM only vacation
        $this->tearDown();
        $this->setUp();
        
        $amVacation = $this->createVacation('2024-01-16', '2024-01-16', 1);
        $this->approveVacation($amVacation);

        $summary = $this->vacationService->getVacationSummary($this->user, '2024-01-16');
        
        $this->assertEquals('vacation', $summary['status']);
        $this->assertStringContains('V (AM)', $summary['display']);
        $this->assertEquals('single_shift', $summary['type']);
    }

    /** @test */
    public function it_handles_bulk_vacation_status()
    {
        // Create vacations for multiple dates
        $vacation1 = $this->createVacation('2024-01-15', '2024-01-15', null); // Full day
        $this->approveVacation($vacation1);
        
        $vacation2 = $this->createVacation('2024-01-16', '2024-01-16', 1); // AM only
        $this->approveVacation($vacation2);

        $users = collect([$this->user]);
        $dates = ['2024-01-15', '2024-01-16', '2024-01-17'];
        
        $results = $this->vacationService->getBulkVacationStatus($users, $dates);
        
        $userResults = $results[$this->user->id];
        
        // Check 2024-01-15 (full day)
        $this->assertTrue($userResults['2024-01-15']['hasVacation']);
        $this->assertEquals('full_day', $userResults['2024-01-15']['vacationType']);
        
        // Check 2024-01-16 (AM only)
        $this->assertTrue($userResults['2024-01-16']['hasVacation']);
        $this->assertEquals('single_shift', $userResults['2024-01-16']['vacationType']);
        
        // Check 2024-01-17 (no vacation)
        $this->assertFalse($userResults['2024-01-17']['hasVacation']);
    }

    /** @test */
    public function it_gets_affected_shifts()
    {
        // Full day vacation
        $vacation = $this->createVacation('2024-01-15', '2024-01-15', null);
        $this->approveVacation($vacation);

        $affectedShifts = $this->vacationService->getVacationAffectedShifts($this->user, '2024-01-15');
        
        $this->assertContains(1, $affectedShifts);
        $this->assertContains(2, $affectedShifts);

        // AM only vacation
        $this->tearDown();
        $this->setUp();
        
        $amVacation = $this->createVacation('2024-01-16', '2024-01-16', 1);
        $this->approveVacation($amVacation);

        $affectedShifts = $this->vacationService->getVacationAffectedShifts($this->user, '2024-01-16');
        
        $this->assertContains(1, $affectedShifts);
        $this->assertNotContains(2, $affectedShifts);
    }

    private function createVacation(string $fromDate, string $toDate, ?int $shiftId): Vacation
    {
        return Vacation::create([
            'user_id' => $this->user->id,
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'shift_id' => $shiftId,
            'vacation_type_id' => $this->vacationType->id,
            'notes' => 'Test vacation'
        ]);
    }

    private function approveVacation(Vacation $vacation): void
    {
        PlanVisitDetails::create([
            'visitable_id' => $vacation->id,
            'visitable_type' => Vacation::class,
            'approval' => 1
        ]);
    }
}
