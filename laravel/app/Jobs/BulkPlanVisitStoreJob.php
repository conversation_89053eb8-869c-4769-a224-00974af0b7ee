<?php

namespace App\Jobs;

use App\Services\Plans\AdvancedPlanVisitStoreService;
use App\Services\Plans\PlanVisitStoreService;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class BulkPlanVisitStoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;
    public $backoff = [10, 30, 60]; // Exponential backoff

    private array $planVisits;
    private int $userId;
    private ?string $fromDate;

    /**
     * Create a new job instance.
     */
    public function __construct(array $planVisits, int $userId, ?string $fromDate = '')
    {
        $this->planVisits = $planVisits;
        $this->userId = $userId;
        $this->fromDate = $fromDate;
        
        // Use high priority queue for plan operations
        $this->onQueue('high');
    }

    /**
     * Execute the job.
     */
    public function handle(AdvancedPlanVisitStoreService $storeService): array
    {
        try {
            Log::info("Starting bulk plan visit store job", [
                'user_id' => $this->userId,
                'plan_count' => count($this->planVisits),
                'from_date' => $this->fromDate
            ]);

            // Set the authenticated user context
            auth()->loginUsingId($this->userId);

            $result = $storeService->store($this->planVisits, $this->fromDate);

            Log::info("Completed bulk plan visit store job", [
                'user_id' => $this->userId,
                'created_count' => count($result['data']),
                'error_count' => count($result['errors'])
            ]);

            return $result;

        } catch (\Exception $e) {
            Log::error("Failed bulk plan visit store job", [
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Bulk plan visit store job failed permanently", [
            'user_id' => $this->userId,
            'plan_count' => count($this->planVisits),
            'error' => $exception->getMessage()
        ]);

        // You could notify the user or take other recovery actions here
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['plan-visits', 'user:' . $this->userId, 'bulk-operation'];
    }
}
