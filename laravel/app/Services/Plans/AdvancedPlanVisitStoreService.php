<?php

namespace App\Services\Plans;

use App\Account;
use App\ActualVisit;
use App\ActualVisitSetting;
use App\DivisionType;
use App\ErrorMessages;
use App\Exceptions\CrmException;
use App\Jobs\SendPlanCreatedNotificationJob;
use App\Line;
use App\Mail\PlanCreation;
use App\Models\LinkedPharmacy;
use App\Models\PlanLevel;
use App\Models\PlanVisitLimit;
use App\PlanSetting;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Position;
use App\Services\Enums\PlanVisitSettings\DeletePlan;
use App\Services\FrequencyTypeValidationService;
use App\Setting;
use App\Shift;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;

class AdvancedPlanVisitStoreService
{
    private bool $perType;
    private bool $isExceedFrequency;
    private bool $sendByMail;
    private DeletePlan $planEditAndDelete;

    // Cache TTL in seconds (e.g., 30 minutes)
    private const CACHE_TTL = 1800;

    public function __construct()
    {
        $this->loadPlanSettings();
    }

    /**
     * Load plan settings with caching
     */
    private function loadPlanSettings(): void
    {
        $settings = Cache::remember('plan_settings', self::CACHE_TTL, function () {
            return [
                'perType' => PlanSetting::where('key', 'plan_shift')->value('value') == 'per type',
                'isExceedFrequency' => PlanSetting::where('key', 'close_plan_if_user_meet_frequency')->value('value') == 'yes',
                'sendByMail' => Setting::where('key', 'notification_by_mail')->value('value') == 'Yes',
                'planEditAndDelete' => DeletePlan::from(PlanSetting::where('key', 'delete_plan')->value('value')),
            ];
        });

        $this->perType = $settings['perType'];
        $this->isExceedFrequency = $settings['isExceedFrequency'];
        $this->sendByMail = $settings['sendByMail'];
        $this->planEditAndDelete = $settings['planEditAndDelete'];
    }

    /**
     * Store plan visits and return created data - Optimized for concurrent requests
     */
    public function store(array $planVisits, ?string $fromDate = '')
    {
        /**@var User */
        $user = Auth::user();
        $lineId = $planVisits[0]["line_id"];
        $line = Line::find($lineId);

        $hasApprovable = $this->checkApprovable($user, $line);
        $currentYear = Carbon::parse($fromDate)->year;

        $frequencyDates = [
            'from' => Carbon::now()->startOfMonth(),
            'to' => Carbon::now()->endOfMonth()
        ];

        // Pre-validate and prepare data outside transaction to reduce lock time
        $preparedData = $this->prepareAndValidateData($planVisits, $user, $line, $currentYear, $frequencyDates);

        $planVisitsData = [];
        $createdPlanVisits = collect();

        // Shorter, more focused transaction
        DB::transaction(function () use (
            $line,
            $user,
            &$planVisitsData,
            &$createdPlanVisits,
            $hasApprovable,
            $preparedData
        ) {
            $planVisitsData = $preparedData['validPlanVisits'];

            if (empty($planVisitsData)) {
                return;
            }

            $this->checkPlanLimitation($user, $line, $planVisitsData);

            // Optimized bulk insert with better performance
            $this->bulkInsertPlanVisits($planVisitsData);

            // More efficient retrieval of created visits
            $createdPlanVisits = $this->getCreatedPlanVisits($user, $planVisitsData);

            // Bulk insert plan visit details with better performance
            $this->bulkInsertPlanVisitDetails($createdPlanVisits, $hasApprovable);
        });

        // Move non-critical operations outside transaction
        $this->handlePostTransactionOperations($user, $line);

        $data = $this->formatPlanData($createdPlanVisits);

        return ['data' => $data, 'errors' => $preparedData['pharmacyErrorVisits']];
    }

    /**
     * Prepare and validate data outside transaction for better performance
     */
    private function prepareAndValidateData(array $planVisits, User $user, Line $line, int $currentYear, array $frequencyDates): array
    {
        $linkedSetting = Cache::remember('linked_pharmacy_setting', self::CACHE_TTL, function () {
            return PlanSetting::where('key', 'linked_pharmacy_required')->value('value');
        });

        // Bulk fetch all required data upfront
        $accountIds = array_column($planVisits, 'account_id');

        // Optimized account and shift fetching
        $accounts = Cache::remember("accounts_shifts_" . md5(serialize($accountIds)), 300, function () use ($accountIds) {
            return Account::whereIn('id', $accountIds)
                ->with('type.shift')
                ->get();
        });

        $accountShifts = $accounts->mapWithKeys(function ($account) {
            return [$account->id => optional($account->type->shift)->id];
        });

        // Bulk check linked pharmacies if required
        $linkedPharmacies = collect();
        if ($linkedSetting == 'yes') {
            $linkedPharmacies = Cache::remember("linked_pharmacies_" . md5(serialize($accountIds)), 300, function () use ($accountIds) {
                return LinkedPharmacy::whereIn('account_id', $accountIds)
                    ->pluck('account_id')
                    ->flip();
            });
        }

        // Get plan level once
        $isPerDoctor = Cache::remember("plan_level_line_{$line->id}", self::CACHE_TTL, function () use ($line) {
            return PlanLevel::where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $line->id))
                ->first()->level == 'Doctor';
        });

        // Bulk frequency validation if needed
        if ($isPerDoctor && $this->isExceedFrequency) {
            $this->bulkValidateFrequencies($planVisits, $user, $frequencyDates);
        }

        $validPlanVisits = [];
        $pharmacyErrorVisits = [];

        // Process all visits
        foreach ($planVisits as $planVisit) {
            $planDate = Carbon::parse($planVisit['visit_date'])->setYear($currentYear);
            $planDateTime = $planDate->setTimeFromTimeString($planVisit['time'] ?? '00:00:00');

            if ($this->perType) {
                $planVisit['shift_id'] = $planVisit['shift_id'] ?? $accountShifts[$planVisit['account_id']] ?? null;
            }

            // Optimized linked pharmacy check
            if ($linkedSetting == 'yes') {
                if ($linkedPharmacies->has($planVisit['account_id'])) {
                    $validPlanVisits[] = $this->preparePlanVisitData($planVisit, $user, $planDateTime);
                } else {
                    $pharmacyErrorVisits[] = $planVisit;
                }
            } else {
                $validPlanVisits[] = $this->preparePlanVisitData($planVisit, $user, $planDateTime);
            }
        }

        return [
            'validPlanVisits' => $validPlanVisits,
            'pharmacyErrorVisits' => $pharmacyErrorVisits
        ];
    }

    /**
     * Bulk validate frequencies for better performance
     */
    private function bulkValidateFrequencies(array $planVisits, User $user, array $frequencyDates): void
    {
        // Extract unique doctor IDs for bulk frequency checking
        $doctorIds = array_unique(array_column($planVisits, 'account_dr_id'));

        if (empty($doctorIds)) {
            return;
        }

        // Bulk fetch existing visit counts
        $existingVisitCounts = ActualVisit::where('user_id', $user->id)
            ->whereBetween('visit_date', [$frequencyDates['from'], $frequencyDates['to']])
            ->whereIn('account_dr_id', $doctorIds)
            ->groupBy('account_dr_id')
            ->selectRaw('account_dr_id, COUNT(*) as visit_count')
            ->pluck('visit_count', 'account_dr_id');

        // Bulk fetch frequencies using optimized service
        $doctorData = collect($planVisits)->map(function ($visit) {
            return [
                'doctor_id' => $visit['account_dr_id'],
                'account_id' => $visit['account_id'],
                'line_id' => $visit['line_id'],
            ];
        })->unique()->values()->toArray();

        $frequencies = (new \App\Services\OptimizedFrequencyService)->getBulkFrequencies(
            $doctorData,
            $frequencyDates['from']->month,
            $frequencyDates['from']->year
        );

        // Validate each visit against bulk-fetched data
        foreach ($planVisits as $planVisit) {
            $doctorId = $planVisit['account_dr_id'];
            $existingCount = $existingVisitCounts->get($doctorId, 0);
            $frequency = $frequencies->get($doctorId, 0);

            if ($existingCount >= $frequency) {
                $errorMessage = Cache::remember('error_meet_frequency', self::CACHE_TTL, function () {
                    return \App\ErrorMessages::where("slug", "meet_frequency")->value("message_en");
                });
                throw new \Exception("There is Doctor " . $errorMessage);
            }
        }
    }

    /**
     * Optimized bulk insert for plan visits
     */
    private function bulkInsertPlanVisits(array $planVisitsData): void
    {
        // Use chunked insert for better memory management with large datasets
        $chunks = array_chunk($planVisitsData, 500);

        foreach ($chunks as $chunk) {
            PlanVisit::insert($chunk);
        }
    }

    /**
     * More efficient retrieval of created plan visits
     */
    private function getCreatedPlanVisits(User $user, array $planVisitsData): Collection
    {
        if (empty($planVisitsData)) {
            return collect();
        }

        // Create more specific query conditions to reduce result set
        $visitDates = collect($planVisitsData)->pluck('visit_date')->unique()->values();
        $accountIds = collect($planVisitsData)->pluck('account_id')->unique()->values();

        return PlanVisit::where('user_id', $user->id)
            ->whereDoesntHave('details')
            ->whereIn('visit_date', $visitDates)
            ->whereIn('account_id', $accountIds)
            ->where('created_at', '>=', now()->subMinutes(5)) // Only recent records
            ->get();
    }

    /**
     * Optimized bulk insert for plan visit details
     */
    private function bulkInsertPlanVisitDetails(Collection $createdPlanVisits, bool $hasApprovable): void
    {
        if ($createdPlanVisits->isEmpty()) {
            return;
        }

        $planVisitDetailsData = $createdPlanVisits->map(function ($planVisit) use ($hasApprovable) {
            return [
                'visitable_id' => $planVisit->id,
                'visitable_type' => PlanVisit::class,
                'approval' => $hasApprovable ? null : 1,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        })->toArray();

        // Use chunked insert for better performance
        $chunks = array_chunk($planVisitDetailsData, 500);

        foreach ($chunks as $chunk) {
            PlanVisitDetails::insert($chunk);
        }
    }

    /**
     * Handle operations that don't need to be in transaction
     */
    private function handlePostTransactionOperations(User $user, Line $line): void
    {
        // Send mail if needed (moved outside transaction)
        if ($this->sendByMail) {
            $manager = Cache::remember("approvable_manager_{$user->id}_{$line->id}", 300, function () use ($user, $line) {
                return $user->approvableUsers($line->id)->first();
            });

            if ($manager && $manager->email) {
                \Illuminate\Support\Facades\Mail::to($manager->email)->queue(new \App\Mail\PlanCreation(
                    'Hello, Doctor : ' . $manager->fullname . ' there are plans created from doctor: ' . $user->fullname,
                    'Plan Creation'
                ));
            }
        }

        // Queue notification job (already optimized)
        $this->sendNotifications($user, $line->id);
    }



    /**
     * Check plan limitations - Optimized version
     */
    private function checkPlanLimitation(User $user, Line $line, array $planVisitsData): void
    {
        $planLimitSetting = Cache::remember('plan_limit_setting', self::CACHE_TTL, function () {
            return PlanSetting::where('key', 'plan_limit')->value('value');
        });

        if ($planLimitSetting != 'yes' || empty($planVisitsData)) {
            return;
        }

        // Determine user type and type_id
        [$type, $type_id] = $this->getUserRoleTypeInfo($user, $line);

        // Get cached settings
        $cacheData = Cache::remember("plan_limit_cache_data_{$user->id}_{$line->id}", self::CACHE_TTL, function () {
            return [
                'shifts' => Shift::select('id', 'name')->get(),
                'actualExtraTime' => ActualVisitSetting::where('key', 'actual_extra_time')->value('value')
            ];
        });

        $shifts = $cacheData['shifts'];
        $actualExtraTime = $cacheData['actualExtraTime'];

        // Calculate date range once
        $saturday = Carbon::now()->startOfWeek()->addDays(5)->setTime($actualExtraTime, 0, 0);
        $wednesday = $saturday->copy()->addDays(4)->setTime(23, 59, 59);

        // Group visits by shift for batch processing
        $visitsByShift = collect($planVisitsData)->groupBy('shift_id');

        foreach ($visitsByShift as $shiftId => $shiftVisits) {
            $shift = $shifts->firstWhere('id', $shiftId);
            if (!$shift) {
                continue;
            }

            // Filter visits for date range
            $filteredVisits = $shiftVisits->filter(function ($visit) use ($saturday, $wednesday) {
                $visitDate = Carbon::parse($visit['visit_date']);
                return $visitDate->between($saturday, $wednesday);
            });

            if ($filteredVisits->isEmpty()) {
                continue;
            }

            // Get plan limit with better caching
            $cacheKey = "plan_limit_{$type}_{$type_id}_{$line->id}_{$shiftId}";
            $planLimit = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($type_id, $type, $line, $shiftId) {
                return PlanVisitLimit::where('roleable_id', $type_id)
                    ->where('roleable_type', $type)
                    ->where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $line->id))
                    ->where('type', 'Weekly')
                    ->where('shift_id', $shiftId)
                    ->first();
            });

            if (!$planLimit || $planLimit->limit <= 0) {
                continue;
            }

            // Optimized existing plans count with better caching
            $existingCountKey = "existing_plans_count_{$user->id}_{$line->id}_{$shiftId}_{$saturday->format('Y-m-d')}_{$wednesday->format('Y-m-d')}";
            $plansCount = Cache::remember($existingCountKey, 300, function () use ($user, $line, $shiftId, $saturday, $wednesday) {
                return PlanVisit::where('line_id', $line->id)
                    ->where('user_id', $user->id)
                    ->where('shift_id', $shiftId)
                    ->whereBetween('visit_date', [$saturday, $wednesday])
                    ->count();
            });

            // Check if limit is exceeded
            $total = $plansCount + $filteredVisits->count();
            if ($total > $planLimit->limit) {
                throw new \Exception('Plan Limit ' . $planLimit->limit . ' and plans count ' . $total . ' at shift ' . $shift->name);
            }
        }
    }

    /**
     * Get user role type information
     */
    private function getUserRoleTypeInfo(User $user, Line $line): array
    {
        if ($user->hasPosition() && !$user->hasDivision($line)) {
            return [Position::class, $user->position()->id];
        }

        return [DivisionType::class, $user->divisionType($line)->id];
    }

    /**
     * Check if user's role requires approval
     */
    private function checkApprovable(User $user, Line $line): bool
    {
        $cacheKey = "approvable_user_{$user->id}_line_{$line->id}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user, $line) {
            if ($user->hasPosition() && !$user->hasDivision($line)) {
                return $user->position()->planable->first()->approvables()
                    ->wherePivot('request_type', PlanVisit::class)
                    ->exists();
            }

            return $user->division($line)?->divisionType?->planable?->where('line_id', $line->id)
                ->first()?->approvables()
                ->wherePivot('request_type', PlanVisit::class)
                ->exists() ?? false;
        });
    }

    /**
     * Prepare plan visit data array
     */
    private function preparePlanVisitData(array $planVisit, User $user, Carbon $planDateTime): array
    {
        return [
            'user_id' => $user->id,
            'line_id' => $planVisit['line_id'],
            'div_id' => $planVisit['div_id'],
            'account_id' => $planVisit['account_id'],
            'account_dr_id' => $planVisit['account_dr_id'],
            'shift_id' => $planVisit['shift_id'],
            'visit_date' => $planDateTime,
            'visit_type' => $planVisit['visit_type'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }



    /**
     * Send notifications through job queue
     */
    private function sendNotifications(User $user, int $lineId): void
    {
        SendPlanCreatedNotificationJob::dispatch($user, $lineId)->onQueue('high');
    }

    /**
     * Format plan data for response
     */
    private function formatPlanData(Collection $data): Collection
    {
        return $data->map(function ($plan) {
            $approval = $plan->details->approval;
            $isApproved = $approval === 1;

            return [
                'Id' => $plan->id,
                'Shift' => $plan->shift_id,
                'IsReadOnly' => match ($this->planEditAndDelete) {
                    DeletePlan::AFTER_APPROVAL => !$isApproved,
                    DeletePlan::BEFORE_APPROVAL => !($approval === null),
                    DeletePlan::CANT_EDIT_DELETE => true,
                },
                'CalendarId' => $approval === null ? 1 : (!!$approval ? 2 : 3)
            ];
        });
    }
}
