<?php

namespace App\Services;

use App\User;
use App\Vacation;
use App\Shift;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class VacationTrackingService
{
    private const CACHE_TTL = 1800; // 30 minutes

    /**
     * Check if user has vacation for a specific date and determine which shifts are affected
     * 
     * @param User $user
     * @param string $date
     * @param int|null $shiftId
     * @return array ['hasVacation' => bool, 'affectedShifts' => array, 'vacationType' => string]
     */
    public function getUserVacationStatus(User $user, string $date, ?int $shiftId = null): array
    {
        $targetDate = Carbon::parse($date);
        $cacheKey = "vacation_status_{$user->id}_{$targetDate->format('Y-m-d')}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user, $targetDate, $shiftId) {
            return $this->calculateVacationStatus($user, $targetDate, $shiftId);
        });
    }

    /**
     * Get vacation status for multiple users and dates (bulk operation)
     * 
     * @param Collection $users
     * @param array $dates
     * @return array
     */
    public function getBulkVacationStatus(Collection $users, array $dates): array
    {
        $results = [];
        
        // Get all vacations for the users and date range in one query
        $userIds = $users->pluck('id')->toArray();
        $fromDate = min($dates);
        $toDate = max($dates);
        
        $vacations = $this->getBulkVacations($userIds, $fromDate, $toDate);
        
        foreach ($users as $user) {
            $userVacations = $vacations->where('user_id', $user->id);
            $results[$user->id] = [];
            
            foreach ($dates as $date) {
                $results[$user->id][$date] = $this->calculateVacationStatusFromCollection(
                    $userVacations, 
                    Carbon::parse($date)
                );
            }
        }
        
        return $results;
    }

    /**
     * Enhanced hasVacations method that handles AM/PM and full-day logic
     * 
     * @param User $user
     * @param string $date
     * @param int|null $shiftId
     * @return bool
     */
    public function hasVacations(User $user, string $date, ?int $shiftId = null): bool
    {
        $vacationStatus = $this->getUserVacationStatus($user, $date, $shiftId);
        
        if (!$vacationStatus['hasVacation']) {
            return false;
        }
        
        // If no specific shift requested, return true if any vacation exists
        if ($shiftId === null) {
            return true;
        }
        
        // Check if the specific shift is affected
        return in_array($shiftId, $vacationStatus['affectedShifts']) || 
               $vacationStatus['vacationType'] === 'full_day';
    }

    /**
     * Get all shifts that should show vacation for a user on a specific date
     * 
     * @param User $user
     * @param string $date
     * @return array
     */
    public function getVacationAffectedShifts(User $user, string $date): array
    {
        $vacationStatus = $this->getUserVacationStatus($user, $date);
        
        if (!$vacationStatus['hasVacation']) {
            return [];
        }
        
        // If full day vacation or has both AM and PM, return all shifts
        if ($vacationStatus['vacationType'] === 'full_day' || $vacationStatus['vacationType'] === 'both_shifts') {
            return $this->getAllShiftIds();
        }
        
        return $vacationStatus['affectedShifts'];
    }

    /**
     * Calculate vacation status for a user on a specific date
     * 
     * @param User $user
     * @param Carbon $targetDate
     * @param int|null $shiftId
     * @return array
     */
    private function calculateVacationStatus(User $user, Carbon $targetDate, ?int $shiftId = null): array
    {
        $vacations = Vacation::where('user_id', $user->id)
            ->where('from_date', '<=', $targetDate->toDateString())
            ->where('to_date', '>=', $targetDate->toDateString())
            ->whereHas('details', fn($q) => $q->where('approval', 1))
            ->get();

        return $this->calculateVacationStatusFromCollection($vacations, $targetDate);
    }

    /**
     * Calculate vacation status from a collection of vacations
     * 
     * @param Collection $vacations
     * @param Carbon $targetDate
     * @return array
     */
    private function calculateVacationStatusFromCollection(Collection $vacations, Carbon $targetDate): array
    {
        if ($vacations->isEmpty()) {
            return [
                'hasVacation' => false,
                'affectedShifts' => [],
                'vacationType' => 'none'
            ];
        }

        $affectedShifts = [];
        $hasFullDay = false;
        
        foreach ($vacations as $vacation) {
            // Check if vacation period includes the target date
            if (!$targetDate->between($vacation->from_date, $vacation->to_date)) {
                continue;
            }
            
            // Full day vacation (shift_id is null)
            if ($vacation->shift_id === null) {
                $hasFullDay = true;
                break;
            }
            
            // Specific shift vacation
            if (!in_array($vacation->shift_id, $affectedShifts)) {
                $affectedShifts[] = $vacation->shift_id;
            }
        }

        // Determine vacation type
        $vacationType = 'none';
        if ($hasFullDay) {
            $vacationType = 'full_day';
        } elseif (count($affectedShifts) >= 2) {
            // Has both AM and PM (or multiple shifts)
            $vacationType = 'both_shifts';
        } elseif (count($affectedShifts) === 1) {
            $vacationType = 'single_shift';
        }

        return [
            'hasVacation' => $hasFullDay || !empty($affectedShifts),
            'affectedShifts' => $hasFullDay ? $this->getAllShiftIds() : $affectedShifts,
            'vacationType' => $vacationType
        ];
    }

    /**
     * Get bulk vacations for multiple users and date range
     * 
     * @param array $userIds
     * @param string $fromDate
     * @param string $toDate
     * @return Collection
     */
    private function getBulkVacations(array $userIds, string $fromDate, string $toDate): Collection
    {
        $cacheKey = "bulk_vacations_" . md5(serialize($userIds) . $fromDate . $toDate);
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userIds, $fromDate, $toDate) {
            return Vacation::whereIn('user_id', $userIds)
                ->where(function ($query) use ($fromDate, $toDate) {
                    $query->whereBetween('from_date', [$fromDate, $toDate])
                          ->orWhereBetween('to_date', [$fromDate, $toDate])
                          ->orWhere(function ($q) use ($fromDate, $toDate) {
                              $q->where('from_date', '<=', $fromDate)
                                ->where('to_date', '>=', $toDate);
                          });
                })
                ->whereHas('details', fn($q) => $q->where('approval', 1))
                ->get();
        });
    }

    /**
     * Get all available shift IDs
     * 
     * @return array
     */
    private function getAllShiftIds(): array
    {
        return Cache::remember('all_shift_ids', self::CACHE_TTL, function () {
            return Shift::pluck('id')->toArray();
        });
    }

    /**
     * Clear vacation cache for a specific user
     * 
     * @param int $userId
     * @param string|null $date
     */
    public function clearVacationCache(int $userId, ?string $date = null): void
    {
        if ($date) {
            $cacheKey = "vacation_status_{$userId}_{$date}";
            Cache::forget($cacheKey);
        } else {
            // Clear all vacation-related cache for the user
            $pattern = "vacation_status_{$userId}_*";
            // Note: This would require a cache implementation that supports pattern deletion
            // For now, we'll just clear the bulk cache
            Cache::forget('all_shift_ids');
        }
    }

    /**
     * Get vacation summary for reporting
     * 
     * @param User $user
     * @param string $date
     * @return array
     */
    public function getVacationSummary(User $user, string $date): array
    {
        $vacationStatus = $this->getUserVacationStatus($user, $date);
        
        if (!$vacationStatus['hasVacation']) {
            return [
                'status' => 'no_vacation',
                'display' => '',
                'color' => ''
            ];
        }

        $display = 'v';
        $color = 'brown';

        switch ($vacationStatus['vacationType']) {
            case 'full_day':
                $display = 'V (Full Day)';
                break;
            case 'both_shifts':
                $display = 'V (AM+PM)';
                break;
            case 'single_shift':
                $shiftNames = $this->getShiftNames($vacationStatus['affectedShifts']);
                $display = 'V (' . implode(', ', $shiftNames) . ')';
                break;
        }

        return [
            'status' => 'vacation',
            'display' => $display,
            'color' => $color,
            'type' => $vacationStatus['vacationType'],
            'affected_shifts' => $vacationStatus['affectedShifts']
        ];
    }

    /**
     * Get shift names by IDs
     * 
     * @param array $shiftIds
     * @return array
     */
    private function getShiftNames(array $shiftIds): array
    {
        return Cache::remember('shift_names_' . md5(serialize($shiftIds)), self::CACHE_TTL, function () use ($shiftIds) {
            return Shift::whereIn('id', $shiftIds)->pluck('name')->toArray();
        });
    }
}
