<?php

namespace App\Services;

use App\Account;
use App\Helpers\LogActivity;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Service for updating account locations in bulk
 * 
 * This service handles bulk updates of account location coordinates
 * with support for logging, validation, and transaction management.
 * 
 * Laravel Octane Compatible:
 * - No static state
 * - Proper dependency injection
 * - Stateless design
 */
class AccountLocationUpdateService
{
    /**
     * Update multiple account locations in a single transaction
     *
     * @param array $updates Array of account location updates
     * @param array|null $metadata Optional metadata about the analysis
     * @return array Result with count of updated accounts
     */
    public function bulkUpdateLocations(array $updates, ?array $metadata = null): array
    {
        Log::info('Starting bulk account location update', [
            'updates_count' => count($updates),
            'metadata' => $metadata
        ]);

        try {
            $updatedCount = 0;

            DB::beginTransaction();

            foreach ($updates as $update) {
                $account = Account::findOrFail($update['account_id']);

                // Update account location
                $account->accountlines()
                    ->update([
                        'll' =>  $update['new_latitude'],
                        'lg' => $update['new_longitude'],
                    ]);

                // Log the activity with additional context
                $this->logLocationUpdate($account, $update);

                $updatedCount++;
            }

            DB::commit();

            Log::info('Bulk account location update completed successfully', [
                'updated_count' => $updatedCount,
                'analysis_date' => $metadata['analysis_date'] ?? null
            ]);

            return [
                'success' => true,
                'updated_count' => $updatedCount,
                'message' => "Successfully updated {$updatedCount} account location" . ($updatedCount !== 1 ? 's' : '')
            ];
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Error during bulk account location update', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'updated_count' => 0,
                'message' => 'Failed to update account locations: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Log account location update activity with context
     *
     * @param Account $account The account being updated
     * @param array $updateData The update data with context
     * @return void
     */
    private function logLocationUpdate(Account $account, array $updateData): void
    {
        $model_id = $account->id;
        $model_type = 'App\Account';

        // Include location change details in the log context
        $context = [
            'previous_latitude' => $updateData['current_latitude'] ?? null,
            'previous_longitude' => $updateData['current_longitude'] ?? null,
            'new_latitude' => $updateData['new_latitude'],
            'new_longitude' => $updateData['new_longitude'],
            'confidence_score' => $updateData['confidence_score'] ?? null,
            'visit_frequency' => $updateData['visit_frequency'] ?? null,
            'visits_count' => $updateData['visits_count'] ?? null,
            'average_distance_improvement' => $updateData['average_distance_improvement'] ?? null,
            'reason' => $updateData['reason'] ?? 'Bulk location update'
        ];

        // Log the activity with the context
        LogActivity::addLog($model_id, $model_type, null, 'edit_accounts', $context);
    }
}
