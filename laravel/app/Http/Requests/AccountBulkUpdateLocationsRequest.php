<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AccountBulkUpdateLocationsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'updates' => ['required', 'array', 'min:1'],
            'updates.*.account_id' => ['required', 'integer', 'exists:accounts,id'],
            'updates.*.account_name' => ['nullable', 'string'],
            'updates.*.current_latitude' => ['nullable', 'numeric'],
            'updates.*.current_longitude' => ['nullable', 'numeric'],
            'updates.*.new_latitude' => ['required', 'numeric'],
            'updates.*.new_longitude' => ['required', 'numeric'],
            'updates.*.confidence_score' => ['nullable', 'integer', 'min:0', 'max:100'],
            'updates.*.visit_frequency' => ['nullable', 'integer', 'min:0'],
            'updates.*.visits_count' => ['nullable', 'integer', 'min:0'],
            'updates.*.average_distance_improvement' => ['nullable', 'numeric', 'min:0'],
            'updates.*.reason' => ['nullable', 'string'],
            'analysis_metadata' => ['nullable', 'array'],
            'analysis_metadata.distance_threshold' => ['nullable', 'numeric', 'min:0'],
            'analysis_metadata.analysis_date' => ['nullable', 'date'],
            'analysis_metadata.total_accounts_analyzed' => ['nullable', 'integer', 'min:0'],
            'analysis_metadata.total_visits_analyzed' => ['nullable', 'integer', 'min:0'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'updates.required' => 'At least one account update is required',
            'updates.*.account_id.required' => 'Account ID is required for each update',
            'updates.*.account_id.exists' => 'Account ID does not exist',
            'updates.*.new_latitude.required' => 'New latitude is required for each update',
            'updates.*.new_longitude.required' => 'New longitude is required for each update',
        ];
    }
}
