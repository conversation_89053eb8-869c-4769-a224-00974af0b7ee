<?php

namespace App\Http\Controllers;

use App\Http\Requests\PlanVisitStoreRequest;
use App\Jobs\BulkPlanVisitStoreJob;
use App\Services\Plans\PlanVisitStoreService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class OptimizedPlanVisitController extends Controller
{
    private PlanVisitStoreService $storeService;

    public function __construct(PlanVisitStoreService $storeService)
    {
        $this->storeService = $storeService;
    }

    /**
     * Store plan visits with optimized performance for concurrent requests
     */
    public function store(PlanVisitStoreRequest $request): JsonResponse
    {
        $planVisits = $request->validated()['planVisits'];
        $fromDate = $request->input('fromDate', '');
        $user = Auth::user();

        // Determine processing strategy based on request size and system load
        $shouldUseAsync = $this->shouldUseAsyncProcessing($planVisits, $user);

        try {
            if ($shouldUseAsync) {
                return $this->handleAsyncProcessing($planVisits, $user, $fromDate);
            } else {
                return $this->handleSyncProcessing($planVisits, $fromDate);
            }
        } catch (\Exception $e) {
            Log::error('Plan visit store failed', [
                'user_id' => $user->id,
                'plan_count' => count($planVisits),
                'error' => $e->getMessage(),
                'async' => $shouldUseAsync
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to store plan visits: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle synchronous processing for smaller requests
     */
    private function handleSyncProcessing(array $planVisits, string $fromDate): JsonResponse
    {
        $result = $this->storeService->store($planVisits, $fromDate);

        return response()->json([
            'success' => true,
            'data' => $result['data'],
            'errors' => $result['errors'],
            'processing_type' => 'synchronous'
        ]);
    }

    /**
     * Handle asynchronous processing for larger requests
     */
    private function handleAsyncProcessing(array $planVisits, $user, string $fromDate): JsonResponse
    {
        // Create a unique job ID for tracking
        $jobId = uniqid('plan_visit_job_', true);
        
        // Store job status in cache
        Cache::put("plan_visit_job_status:{$jobId}", [
            'status' => 'queued',
            'user_id' => $user->id,
            'plan_count' => count($planVisits),
            'created_at' => now()
        ], 3600); // 1 hour TTL

        // Dispatch the job
        BulkPlanVisitStoreJob::dispatch($planVisits, $user->id, $fromDate)
            ->onQueue('high');

        return response()->json([
            'success' => true,
            'message' => 'Plan visits are being processed asynchronously',
            'job_id' => $jobId,
            'processing_type' => 'asynchronous',
            'status_check_url' => route('plan-visits.job-status', ['jobId' => $jobId])
        ], 202);
    }

    /**
     * Check job status for async operations
     */
    public function checkJobStatus(string $jobId): JsonResponse
    {
        $status = Cache::get("plan_visit_job_status:{$jobId}");

        if (!$status) {
            return response()->json([
                'success' => false,
                'message' => 'Job not found or expired'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'job_id' => $jobId,
            'status' => $status
        ]);
    }

    /**
     * Determine if async processing should be used
     */
    private function shouldUseAsyncProcessing(array $planVisits, $user): bool
    {
        $planCount = count($planVisits);
        
        // Use async for large batches
        if ($planCount > 50) {
            return true;
        }

        // Check current system load
        $currentLoad = $this->getCurrentSystemLoad($user);
        
        // Use async if system is under heavy load
        if ($currentLoad > 0.7) {
            return true;
        }

        // Check if user has many concurrent requests
        $concurrentRequests = Cache::get('concurrent-plan-visits:' . $user->id, 0);
        if ($concurrentRequests > 1) {
            return true;
        }

        return false;
    }

    /**
     * Get current system load indicator
     */
    private function getCurrentSystemLoad($user): float
    {
        // Simple load indicator based on concurrent users and cache hits
        $activeUsers = Cache::get('active_plan_users_count', 0);
        $cacheHitRate = Cache::get('plan_cache_hit_rate', 1.0);
        
        // Normalize load between 0 and 1
        $userLoad = min($activeUsers / 100, 1.0); // Assume 100 concurrent users is high load
        $cacheLoad = 1.0 - $cacheHitRate; // Lower cache hit rate = higher load
        
        return ($userLoad + $cacheLoad) / 2;
    }

    /**
     * Get performance metrics for monitoring
     */
    public function getPerformanceMetrics(): JsonResponse
    {
        $user = Auth::user();
        
        $metrics = [
            'concurrent_requests' => Cache::get('concurrent-plan-visits:' . $user->id, 0),
            'rate_limit_remaining' => $this->getRateLimitRemaining($user),
            'system_load' => $this->getCurrentSystemLoad($user),
            'cache_stats' => $this->getCacheStats(),
            'queue_size' => $this->getQueueSize()
        ];

        return response()->json([
            'success' => true,
            'metrics' => $metrics
        ]);
    }

    /**
     * Get remaining rate limit attempts
     */
    private function getRateLimitRemaining($user): int
    {
        $key = 'plan-visit-store:' . $user->id;
        $maxAttempts = 10;
        $attempts = Cache::get($key . ':attempts', 0);
        
        return max(0, $maxAttempts - $attempts);
    }

    /**
     * Get cache statistics
     */
    private function getCacheStats(): array
    {
        return [
            'hit_rate' => Cache::get('plan_cache_hit_rate', 1.0),
            'active_keys' => Cache::get('plan_cache_active_keys', 0)
        ];
    }

    /**
     * Get queue size for monitoring
     */
    private function getQueueSize(): int
    {
        // This would depend on your queue driver
        // For Redis, you could check the queue length
        return 0; // Placeholder
    }
}
