<?php

namespace App\Http\Controllers\Analyzer;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Services\ActualService;
use App\Services\DoctorService;
use App\Speciality;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StatisticsAnalyzerController extends Controller
{

    // employee and division
    private bool $isFilterByEmployee = false;

    private Carbon $from;
    private Carbon $to;
    private int $divisionType;
    // visit filter
    private array $visitFilter;
    private User $user;

    private Collection $speciaities;
    private Line $line;

    public function __construct()
    {

    }

    private function setVisitFilters()
    {
        $this->isFilterByEmployee = $this->visitFilter['filter'] == 2;
    }

    private function setDateBoundaries()
    {
        $this->from = Carbon::parse($this->visitFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->visitFilter['toDate'])->endOfDay();
    }

    private function loadLine()
    {
        $this->line = Line::find($this->visitFilter['line']);
    }

    private function loadSpecialities()
    {
        $this->speciaities = $this->line->specialities()->select('specialities.id', DB::raw("SUBSTRING_INDEX(crm_specialities.name,' ', 1) as name"))
            ->when(!empty($this->visitFilter['specialities']), fn ($q) => $q->whereIntegerInRaw("specialities.id", $this->visitFilter['specialities']))->get();
    }

    public function filter(Request $request)
    {
        $this->visitFilter = $request->visitFilter;
        $this->setVisitFilters();
        $this->setDateBoundaries();
        $this->loadLine();
        $this->loadSpecialities();


        $this->divisionType = DivisionType::where('last_level', '=', 1)->value('id');

        $this->isFilterByEmployee
            ? $this->whenFilterByEmployee()
            : $this->whenFilterByDivision();

        return response()->json([
            'data' => $this->speciaities->transform(function (Speciality $speciality) {
                $speciality->percentage = round($speciality->percentage * 100);
                return $speciality;
            })
        ]);
    }

    private function whenFilterByEmployee(): void
    {
        $users = $this->line->users($this->from, $this->to)
            ->when(
                !empty($this->visitFilter['users']),
                fn ($q) => $q->whereIntegerInRaw("line_users.user_id", $this->visitFilter['users'])
            )
            ->get();
        $divisions = collect([]);
        $filteredUsers = Auth::user()->filterUsers($this->line, $users, $this->visitFilter);

        $filteredUsers->each(function (User $user) use ($divisions) {
            $divisions = $divisions->push($user->allBelowDivisions($this->line)
                ->where('division_type_id', '=', $this->divisionType)
                ->where('is_kol', 0));
        });
        $divisions = $divisions->collapse()->unique('id')->pluck('id')->toArray();
        // throw new CrmException($divisions);
        $doctors = (new DoctorService)->getDoctors(
            [$this->line->id],
            $divisions,
            $this->from,
            $this->to
        );
        $this->speciaities->transform(function (Speciality $speciality) use ($filteredUsers, $doctors) {
            $visitsCount = 0;
            foreach ($filteredUsers as $user) {
                $visitsCount += (new ActualService)->getActuals(
                    $user,
                    'users.id',
                    $this->from,
                    $this->to,
                    [],
                    [$this->line->id],
                    [$speciality->id]
                )->count();
            }
            $speciality->visits = $visitsCount;
            $doctorsCount = $doctors->where("speciality_id", $speciality->id)->count();
            $speciality->percentage = $doctorsCount > 0 ? $doctorsCount / $doctors->count() : 0;
            return $speciality;
        });
    }

    private function whenFilterByDivision(): void
    {
        $divisions = $this->line->divisions($this->from, $this->to)
            ->when(!empty($this->visitFilter['divisions']), fn ($q) => $q->whereIntegerInRaw("line_divisions.id", $this->visitFilter['divisions']))->get();
        $belowDivisions = collect([]);
        $divisions->each(function (LineDivision $lineDivision) use ($belowDivisions) {
            $belowDivisions = $belowDivisions->push($lineDivision->getBelowDivisions()
                ->where('division_type_id', '=', $this->divisionType));
        });
        $belowDivisions = $belowDivisions->collapse()->unique('id')->values();
        $belowIds = $belowDivisions->pluck('id')->toArray();
        $doctors = (new DoctorService)->getDoctors(
            [$this->line->id],
            $belowIds,
            $this->from,
            $this->to
        );
        $this->speciaities->transform(function (Speciality $speciality) use ($belowDivisions, $doctors) {
            $visitsCount = 0;
            foreach ($belowDivisions as $belowDivision) {
                $visitsCount += (new ActualService)->getActuals(
                    $belowDivision,
                    'line_divisions.id',
                    $this->from,
                    $this->to,
                    [],
                    [$this->line->id],
                    [$speciality->id]
                )->count();
            }
            $speciality->visits = $visitsCount;

            $doctorsCount = $doctors->where("speciality_id", $speciality->id)->count();
            $speciality->percentage = $doctorsCount > 0 ? ($doctorsCount / $doctors->count()) : 0;

            return $speciality;
        });
        // });
    }
}
