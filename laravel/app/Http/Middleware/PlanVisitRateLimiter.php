<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class PlanVisitRateLimiter
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return $next($request);
        }

        // Create rate limiting key based on user and operation
        $key = 'plan-visit-store:' . $user->id;
        
        // Allow 10 requests per minute for plan visit creation
        $maxAttempts = 10;
        $decayMinutes = 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            
            return response()->json([
                'message' => 'Too many plan visit requests. Please try again in ' . $seconds . ' seconds.',
                'retry_after' => $seconds
            ], 429);
        }

        // Increment the rate limiter
        RateLimiter::hit($key, $decayMinutes * 60);

        // Add concurrent request detection
        $concurrentKey = 'concurrent-plan-visits:' . $user->id;
        $currentRequests = Cache::get($concurrentKey, 0);

        // Limit to 3 concurrent requests per user
        if ($currentRequests >= 3) {
            return response()->json([
                'message' => 'Too many concurrent plan visit requests. Please wait for current operations to complete.',
                'concurrent_limit' => true
            ], 429);
        }

        // Increment concurrent counter
        Cache::put($concurrentKey, $currentRequests + 1, 300); // 5 minutes TTL

        $response = $next($request);

        // Decrement concurrent counter after request
        $currentRequests = Cache::get($concurrentKey, 1);
        if ($currentRequests > 1) {
            Cache::put($concurrentKey, $currentRequests - 1, 300);
        } else {
            Cache::forget($concurrentKey);
        }

        return $response;
    }
}
