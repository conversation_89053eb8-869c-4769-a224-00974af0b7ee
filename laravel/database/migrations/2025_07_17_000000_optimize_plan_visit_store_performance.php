<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // ========================================
        // PLAN VISITS OPTIMIZATION
        // ========================================
        
        Schema::table('planned_visits', function (Blueprint $table) {
            // Composite index for bulk operations and concurrent requests
            $table->index(['user_id', 'line_id', 'visit_date'], 'idx_planned_visits_user_line_date');
            
            // Index for plan limitation checks
            $table->index(['line_id', 'user_id', 'shift_id', 'visit_date'], 'idx_planned_visits_limit_check');
            
            // Index for account-based queries
            $table->index(['account_id', 'visit_date'], 'idx_planned_visits_account_date');
            
            // Index for recent records retrieval
            $table->index(['user_id', 'created_at'], 'idx_planned_visits_user_created');
            
            // Index for doctor-based queries
            $table->index(['account_dr_id', 'visit_date'], 'idx_planned_visits_doctor_date');
        });

        // ========================================
        // LINKED PHARMACIES OPTIMIZATION
        // ========================================
        
        Schema::table('linked_pharmacies', function (Blueprint $table) {
            // Primary index for bulk existence checks
            $table->index(['account_id'], 'idx_linked_pharmacies_account');
            
            // Composite index for user-account queries
            $table->index(['user_id', 'account_id'], 'idx_linked_pharmacies_user_account');
            
            // Index for soft deletes
            $table->index(['deleted_at'], 'idx_linked_pharmacies_deleted');
        });

        // ========================================
        // ACTUAL VISITS OPTIMIZATION FOR FREQUENCY CHECKS
        // ========================================
        
        Schema::table('actual_visits', function (Blueprint $table) {
            // Composite index for frequency validation
            $table->index(['user_id', 'account_dr_id', 'visit_date'], 'idx_actual_visits_frequency_check');
            
            // Index for bulk frequency queries
            $table->index(['account_dr_id', 'visit_date'], 'idx_actual_visits_doctor_date');
        });

        // ========================================
        // PLAN VISIT LIMITS OPTIMIZATION
        // ========================================
        
        Schema::table('plan_visit_limits', function (Blueprint $table) {
            // Composite index for role-based limit checks
            $table->index(['roleable_id', 'roleable_type', 'line_id', 'shift_id'], 'idx_plan_limits_role_line_shift');
            
            // Index for type-based queries
            $table->index(['type', 'shift_id'], 'idx_plan_limits_type_shift');
        });

        // ========================================
        // ACCOUNTS OPTIMIZATION
        // ========================================
        
        Schema::table('accounts', function (Blueprint $table) {
            // Index for type-shift relationship queries
            $table->index(['type_id'], 'idx_accounts_type');
        });

        // ========================================
        // PLAN SETTINGS OPTIMIZATION
        // ========================================
        
        Schema::table('plan_settings', function (Blueprint $table) {
            // Index for key-based lookups
            $table->index(['key'], 'idx_plan_settings_key');
        });

        // ========================================
        // SETTINGS OPTIMIZATION
        // ========================================
        
        Schema::table('settings', function (Blueprint $table) {
            // Index for key-based lookups
            $table->index(['key'], 'idx_settings_key');
        });

        // ========================================
        // ACTUAL VISIT SETTINGS OPTIMIZATION
        // ========================================
        
        Schema::table('actual_visit_settings', function (Blueprint $table) {
            // Index for key-based lookups
            $table->index(['key'], 'idx_actual_visit_settings_key');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('planned_visits', function (Blueprint $table) {
            $table->dropIndex('idx_planned_visits_user_line_date');
            $table->dropIndex('idx_planned_visits_limit_check');
            $table->dropIndex('idx_planned_visits_account_date');
            $table->dropIndex('idx_planned_visits_user_created');
            $table->dropIndex('idx_planned_visits_doctor_date');
        });

        Schema::table('linked_pharmacies', function (Blueprint $table) {
            $table->dropIndex('idx_linked_pharmacies_account');
            $table->dropIndex('idx_linked_pharmacies_user_account');
            $table->dropIndex('idx_linked_pharmacies_deleted');
        });

        Schema::table('actual_visits', function (Blueprint $table) {
            $table->dropIndex('idx_actual_visits_frequency_check');
            $table->dropIndex('idx_actual_visits_doctor_date');
        });

        Schema::table('plan_visit_limits', function (Blueprint $table) {
            $table->dropIndex('idx_plan_limits_role_line_shift');
            $table->dropIndex('idx_plan_limits_type_shift');
        });

        Schema::table('accounts', function (Blueprint $table) {
            $table->dropIndex('idx_accounts_type');
        });

        Schema::table('plan_settings', function (Blueprint $table) {
            $table->dropIndex('idx_plan_settings_key');
        });

        Schema::table('settings', function (Blueprint $table) {
            $table->dropIndex('idx_settings_key');
        });

        Schema::table('actual_visit_settings', function (Blueprint $table) {
            $table->dropIndex('idx_actual_visit_settings_key');
        });
    }
};
