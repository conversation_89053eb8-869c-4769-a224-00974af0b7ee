<template>
  <div class="plan-visits-container">
    <filter-data @Schedule="initialize" />
    <transition name="fade">
      <div v-if="items.length != 0" class="dashboard-card">
        <plan-visits-header :month="month" :saving="saving" @save="store" />

        <plan-visits-stats :total-list="items.length" :planned-visits="plannedVisitsCount"
          :filtered-visits="filteredItems.length" />

        <plan-visits-search :searchTerm.sync="searchTerm" @clearSearch="clearSearch"
          @update:searchTerm="handleSearchInput" />

        <plan-visits-table :items="sortedFilteredItems" :fieldsWithoutDates="fieldsWithoutDates" :dates="dates"
          :selected.sync="selected" :planSelectedStatus="planSelectedStatus" :plan_shift="plan_shift"
          :plan_time="plan_time" :plan_level="plan_level" :sortColumn="sortColumn" :sortDirection="sortDirection"
          :initialized="initialized" :from="from" @sortBy="sortBy" @getLinkedPharmacies="getLinkedPharmacies"
          @togglePlanSelection="togglePlanSelection" @update:selected="updateSelectedData" />

        <div class="card-footer">
          <button class="btn-save" @click="store" :disabled="saving">
            <span v-if="!saving">Save</span>
            <span v-else class="spinner"></span>
          </button>
        </div>
      </div>
    </transition>

    <div v-if="items.length === 0 && initialized" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-calendar-times"></i>
      </div>
      <h3>No Visits to Plan</h3>
      <p>Use the filters above to select a date range and line to plan visits.</p>
    </div>
  </div>
</template>

<script>
import FilterData from "../../components/dailyvisits/FilterData.vue";
import PlanVisitsHeader from "../../components/DailyPlanVisit/PlanVisitsHeader.vue";
import PlanVisitsStats from "../../components/DailyPlanVisit/PlanVisitsStats.vue";
import PlanVisitsSearch from "../../components/DailyPlanVisit/PlanVisitsSearch.vue";
import PlanVisitsTable from "../../components/DailyPlanVisit/PlanVisitsTable.vue";
import axios from 'axios'; // Assuming axios is used globally or imported elsewhere, ensure it's available

export default {
  components: {
    FilterData,
    PlanVisitsHeader,
    PlanVisitsStats,
    PlanVisitsSearch,
    PlanVisitsTable
  },
  data() {
    return {
      items: [],
      fields: [],
      fieldsWithoutDates: [],
      dates: [],
      selected: {}, // This will be managed via sync modifier or event
      planSelectedStatus: {}, // New property to track checkbox state independently
      plan_time: null,
      plan_shift: null,
      plan_level: { level: null }, // Initialize plan_level as an object
      selectedLine: null,
      oldVisits: {},
      month: null,
      from: null,
      to: null,
      initialized: false,
      saving: false,
      searchTerm: "",
      filteredItems: [],
      sortColumn: null, // Added for sorting
      sortDirection: 'asc' // Added for sorting ('asc' or 'desc')
    };
  },
  computed: {
    plannedVisitsCount() {
      let count = 0;
      Object.keys(this.selected).forEach(date => {
        Object.keys(this.selected[date]).forEach(index => {
          if (this.selected[date][index] && this.selected[date][index].line_id !== null) {
            count++;
          }
        });
      });
      return count;
    },
    // sortedFilteredItems is now handled within PlanVisitsTable, but we still need filteredItems
    // Let's keep the filtering logic here for now, and pass filteredItems to the table
    // The sorting logic is moved to the table component's computed property
    sortedFilteredItems() {
      // Filtering logic remains here
      let itemsToFilter = [...this.items];
      if (this.searchTerm.trim()) {
        const searchTermLower = this.searchTerm.toLowerCase();
        itemsToFilter = this.items.filter(item => {
          return (
            (item.account && item.account.toLowerCase().includes(searchTermLower)) ||
            (item.doctor && item.doctor.toLowerCase().includes(searchTermLower)) ||
            (item.group_name && item.group_name.toLowerCase().includes(searchTermLower)) ||
            (item.specialty && item.specialty.toLowerCase().includes(searchTermLower)) ||
            (item.area && item.area.toLowerCase().includes(searchTermLower))
          );
        });
      }
      // Sorting is now done in the child component, just return the filtered list
      // Apply sorting based on sortColumn and sortDirection
      if (!this.sortColumn) return itemsToFilter;

      return [...itemsToFilter].sort((a, b) => {
        const modifier = this.sortDirection === 'asc' ? 1 : -1;
        const aValue = a[this.sortColumn];
        const bValue = b[this.sortColumn];

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return (aValue - bValue) * modifier;
        }
        // Ensure values are strings before comparing
        const strA = String(aValue === null || aValue === undefined ? '' : aValue);
        const strB = String(bValue === null || bValue === undefined ? '' : bValue);
        return strA.localeCompare(strB) * modifier;
      });
    }
  },
  methods: {
    // Methods used by child components (passed as props or event handlers)
    // initialize(planFilter) {
    //   this.initialized = false; // Reset initialized state
    //   this.from = planFilter.fromDate;
    //   this.to = planFilter.toDate;
    //   this.selectedLine = planFilter.line;

    //   axios
    //     .post("/api/get-plan-schedule", {
    //       planFilter
    //     })
    //     .then(response => {
    //       this.parepareItemsAndFields(response.data);
    //       this.initialized = true; // Set initialized after data is processed
    //     })
    //     .catch(error => {
    //       this.showErrorMessage(error);
    //       this.initialized = true; // Ensure initialized is true even on error to show empty state
    //     });
    // },

    parepareItemsAndFields(data) {
      this.items = data.data || [];
      // this.filteredItems = [...this.items]; // filteredItems is now computed
      this.plan_shift = data.plan_shift;
      this.plan_time = data.plan_time;
      this.plan_level = data.plan_level || { level: null }; // Ensure plan_level is an object
      this.oldVisits = data.oldVisits || {};
      this.month = data.month;
      this.dates = data.dates || [];
      this.fieldsWithoutDates = data.fields || [];
      this.fields = [...this.fieldsWithoutDates, ...this.dates];
      const newSelected = {};
      const newPlanSelectedStatus = {};

      // Reset sort state when initializing
      this.sortColumn = null;
      this.sortDirection = 'asc';
      this.searchTerm = ''; // Reset search term

      this.dates.forEach(date => {
        newSelected[date] = {};
        newPlanSelectedStatus[date] = {};
        this.items.forEach(item => {
          const id = this.concatLevel(item);
          let foundOldVisit = false;
          if (this.oldVisits[date]) {
            this.oldVisits[date].forEach(oldVisit => {
              if (
                (this.plan_level.level == "Doctor" &&
                  oldVisit.account_dr_id == item.account_dr_id) ||
                (this.plan_level.level == "Account" &&
                  oldVisit.account_id == item.id) // Use item.id for Account level
              ) {
                newSelected[date][id] = this.defaultPlan(date, oldVisit);
                newPlanSelectedStatus[date][id] = true;
                foundOldVisit = true;
              }
            });
          }
          if (!foundOldVisit) {
            newSelected[date][id] = this.defaultPlan(date);
            newPlanSelectedStatus[date][id] = false;
          }
        });
      });
      this.selected = newSelected;
      this.planSelectedStatus = newPlanSelectedStatus;
      // No need to set initialized here, it's set after axios call completes
    },

    store() {
      this.saving = true;
      let planVisits = [];

      Object.keys(this.selected).forEach(date => {
        Object.keys(this.selected[date]).forEach(index => {
          let plan = this.selected[date][index];
          // Ensure plan exists and is not null before accessing properties
          if (plan && plan.line_id != null && plan.old == false) {
            planVisits.push(plan);
          }
        });
      });

      axios
        .post("/api/plans-daily", {
          planVisits,
          fromDate: this.from,
          toDate: this.to
        })
        .then(response => {
          // Mark saved visits as 'old' to disable controls
          planVisits.forEach(plan => {
            let id = this.concatLevel({ id: plan.account_id, account_dr_id: plan.account_dr_id });
            if (this.selected[plan.visit_date] && this.selected[plan.visit_date][id]) {
              // Use Vue.set or this.$set for reactivity if needed, though direct mutation might work
              this.selected[plan.visit_date][id].old = true;
            }
          });
          this.saving = false;
          // Optionally re-fetch data or update UI state further
          // Re-initializing might be too disruptive, consider targeted updates
        })
        .catch(error => {
          console.log(error)
          this.showErrorMessage(error);
          this.saving = false;
        });
    },

    sortBy(key) {
      if (this.sortColumn === key) {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortColumn = key;
        this.sortDirection = 'asc';
      }
      // The computed property `sortedFilteredItems` will react to these changes
    },

    handleSearchInput(value) {
      this.searchTerm = value;
      // Filtering is handled by the computed property `sortedFilteredItems`
    },

    clearSearch() {
      this.searchTerm = "";
      // Filtering is handled by the computed property `sortedFilteredItems`
    },

    getLinkedPharmacies(item) {
      // Assuming visitData is available or can be derived
      const visitData = { fromDate: this.from, toDate: this.to, line: this.selectedLine };
      axios
        .post(`/api/get-linked-pharmacies`, {
          listFilter: visitData, // Ensure visitData is correctly populated
          item: item
        })
        .then(response => {
          const data = response.data.data;
          this.showPharmaciesModal(data, item);
        })
        .catch(error => {
          this.showErrorMessage(error);
        });
    },

    showPharmaciesModal(pharmacies, item) {
      this.$root.$table("linked Pharmacies:", pharmacies)
    },

    togglePlanSelection(event, item, date) {
      const isChecked = event.target.checked;
      const id = this.concatLevel(item);

      // Ensure the date objects exist
      if (!this.planSelectedStatus[date]) {
        this.$set(this.planSelectedStatus, date, {});
      }
      if (!this.selected[date]) {
        this.$set(this.selected, date, {});
      }

      // Update the independent selected status reactively
      this.$set(this.planSelectedStatus[date], id, isChecked);

      if (isChecked) {
        // If checked, ensure the plan details exist or create them reactively
        if (!this.selected[date][id] || this.selected[date][id].line_id === null) {
          this.$set(this.selected[date], id, this.selectedPlan(item, date));
        }
      } else {
        // If unchecked, reset the plan details to default (empty) reactively
        // Ensure the plan exists before trying to reset it
        if (this.selected[date][id]) {
          this.$set(this.selected[date], id, this.defaultPlan(date));
        }
      }
    },

    updateSelectedData(newSelectedData) {
      // Handle updates from the child table component if using .sync or specific events
      this.selected = newSelectedData;
    },

    // Helper methods (can potentially be moved to a utility file)
    concatLevel(item) {
      // Ensure item and item.id are defined before accessing properties
      if (!item) return null;
      return item.account_dr_id ? `${item.id}_${item.account_dr_id}` : String(item.id);
    },

    defaultPlan(date, orPlan = {}) {
      let isEmpty = Object.keys(orPlan).length === 0;
      return {
        line_id: !isEmpty ? orPlan.line_id : null,
        div_id: !isEmpty ? orPlan.div_id : null,
        account_id: !isEmpty ? orPlan.account_id : null,
        shift_id: !isEmpty ? String(orPlan.shift_id) : (this.plan_shift === 'yes' ? '1' : null), // Default to '1' (AM) only if plan_shift is enabled
        account_dr_id: !isEmpty ? orPlan.account_dr_id : null,
        visit_date: !isEmpty ? orPlan.visit_date : date, // Use orPlan.visit_date if available
        time: !isEmpty ? orPlan.time : "",
        visit_type: 1,
        old: !isEmpty ? true : false
      };
    },

    selectedPlan(item, date) {
      return {
        line_id: this.selectedLine,
        div_id: item.div_id,
        account_id: item.id,
        shift_id: this.plan_shift === 'yes' ? "1" : null, // Only set shift_id if plan_shift is enabled
        account_dr_id:
          this.plan_level.level == "Doctor" ? item.account_dr_id : null,
        visit_date: date,
        visit_type: 1,
        time: "",
        old: false
      };
    },

    // showErrorMessage(error) {
    //   // Implement a more user-friendly error display
    //   const message = error.response?.data?.message || error.message || "An unexpected error occurred.";
    //   this.showErrorMessage(message);
    // }

    // Remove methods that are now fully handled by child components if any
    // e.g., formatFieldName, formatDayName, formatDayNumber, getCellClass, getSortIcon are in PlanVisitsTable
  }
};
</script>

<style>
:root {
  --primary: #3a7bf2;
  --primary-dark: #2c5fc0;
  --primary-light: #eef3ff;
  --success: #0cce6b;
  --warning: #ff7e42;
  --danger: #ff4757;
  --dark: #2d3748;
  --light: #f7fafc;
  --gray: #a0aec0;
  --shadow: rgba(0, 0, 0, 0.1);
}

.plan-visits-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  max-width: 100%;
  margin: 0 auto;
}

.dashboard-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  transition: all 0.3s ease;
  overflow: hidden;
  /* Keep overflow hidden */
}

.card-footer {
  padding: 16px 24px;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
}

/* Keep .btn-save styles if used in footer, or move if only in header */
.btn-save {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-save:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-save:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background: var(--gray);
}

/* Keep spinner styles if used in footer button */
.spinner {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-left-color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #f7fafc;
  border-radius: 16px;
  margin-top: 24px;
  color: var(--gray);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #cbd5e0;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--dark);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Remove styles that were moved to child components */
/* .card-header, .header-title, .title-icon, .period-badge, .header-actions */
/* .stats-bar, .stat-item, .stat-label, .stat-value, .stat-legend, .legend-item, .color-dot */
/* .search-container, .search-input-wrapper, .search-icon, .search-input, .clear-search-btn */
/* .table-container, .data-table, th, td, .date-column, .date-header, etc. */
/* .plan-cell, .plan-controls, .plan-toggle, .toggle-slider, .plan-details, .shifts, .shift-option, .time-input, .visit-status, .status-badge */
</style>